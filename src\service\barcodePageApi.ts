import type { ReportStatus } from '@/enums'
import { http } from '@/http/http'

export interface BarCodePageParams {
  barCode?: string
  barType?: string
  groupBy?: string
  needTotalCount?: boolean
  orderBy?: string
  orderDirection?: OrderDirectionType
  pageIndex?: number
  pageSize?: number
  size?: number
  userId: number
  vendorCode?: string
}
export interface BarCodePageRes {
  data: {
    barCode: string
    barCodeId: number
    orderId: number
    size: number
    reportStatus: ReportStatus | null
    reportStatusName: string
  }[]
  empty: boolean
  errCode: string
  errMessage: string
  notEmpty: boolean
  pageIndex: number
  pageSize: number
  success: boolean
  totalCount: number
  totalPages: number
}
/**
 * 已生成胶片列表
 * @param {object} params qry
 * @param {string} params.barCode 条码
 * @param {string} params.barType 条码类型：EAN13/ITF14
 * @param {string} params.groupBy
 * @param {boolean} params.needTotalCount
 * @param {string} params.orderBy
 * @param {string} params.orderDirection
 * @param {number} params.pageIndex
 * @param {number} params.pageSize
 * @param {number} params.size 放大系数
 * @param {number} params.userId 用户id
 * @param {string} params.vendorCode 厂商识别码
 * @returns
 */
export function barcodePageApi(params: BarCodePageParams) {
  return http.post<BarCodePageRes>('/api/barcodePage', params)
}

export interface DownloadBarCodeParams {
  barCodeIdList: number[]
  downloadType: 1 | 2 // 1:直接下载，2：发送到邮箱
  email?: string
  userId: number
}
export interface DownloadBarCodeRes {
  data: {
    url: string
  }
  errCode: string
  errMessage: string
  success: boolean
}

/**
 * 下载胶片
 * @param {object} params cmd
 * @param {Array} params.barCodeIdList 订单is数组
 * @param {number} params.downloadType 下载方式:1:直接下载，2：发送到邮箱
 * @param {string} params.email 邮箱
 * @param {number} params.userId 用户id
 * @returns
 */
export function downloadBarCodeApi(params: DownloadBarCodeParams) {
  return http.post<DownloadBarCodeRes>('/api/downloadBarCode', params)
}

export interface DownloadByOrderParams {
  downloadType: 1 | 2 // 1:直接下载，2：发送到邮箱
  email?: string
  orderId: number
  userId: number
}
export interface DownloadByOrderRes {
  data: {
    name: string
  }
  errCode: string
  errMessage: string
  success: boolean
}
/**
 * 下载胶片_订单形式
 * @param {object} params cmd
 * @param {number} params.downloadType 下载方式:1:直接下载，2：发送到邮箱
 * @param {string} params.email 邮箱
 * @param {number} params.orderId 订单id
 * @param {number} params.userId 用户id
 * @returns
 */
export function downloadByOrderApi(params: DownloadByOrderParams) {
  return http.post<DownloadByOrderRes>('/api/downloadByOrder', params)
}

export interface SizeListParams {
  userId: number
}
export interface SizeListRes {
  data: {
    size: number
  }[]
  empty: boolean
  errCode: string
  errMessage: string
  notEmpty: boolean
  success: boolean
}
/**
 * 放大系数列表
 * @param {object} params qry
 * @param {number} params.userId 用户id
 * @returns
 */
export function sizeListApi(params: SizeListParams) {
  return http.post<SizeListRes>('/api/sizeList', params)
}

export interface VendorCodeListParams {
  userId: number
}
export interface VendorCodeListRes {
  data: {
    vendorCode: string
  }[]
  empty: boolean
  errCode: string
  errMessage: string
  notEmpty: boolean
  success: boolean
}
/**
 * 厂商码列表
 * @param {object} params qry
 * @param {number} params.userId 用户id
 * @returns
 */
export function vendorCodeListApi(params: VendorCodeListParams) {
  return http.post<VendorCodeListRes>('/api/vendorCodeList', params)
}

// 参数接口
export interface GetCompanyVendorCodeParams {
  companyName: string
}

export interface GetCompanyVendorCodeResData {
  companyName: string
  vendorCode: string
}

// 响应接口
export interface GetCompanyVendorCodeRes {
  data: GetCompanyVendorCodeResData[]
  empty: boolean
  errCode: string
  errMessage: string
  notEmpty: boolean
  success: boolean
}
/**
 * 根据企业名称获取企业产商识别码
 * @param {object} params qry
 * @param {string} params.companyName 企业名称
 * @returns
 */
export function getCompanyVendorCodeApi(params: GetCompanyVendorCodeParams) {
  return http.post<GetCompanyVendorCodeRes>('/api/getCompanyVendorCode', params, {}, true)
}
