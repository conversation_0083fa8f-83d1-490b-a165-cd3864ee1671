# 表单验证工具使用示例

## 基础用法

### 1. 简单错误提示

```typescript
import { showError } from '@/utils/formValidation'

// 显示错误提示
showError('请输入用户名')
```

### 2. 错误提示并滚动到元素

```typescript
import { showErrorAndScrollTo } from '@/utils/formValidation'

// 显示错误提示并滚动到指定元素
showErrorAndScrollTo('请输入密码', 'password')
```

### 3. 批量表单验证

```typescript
import { validateForm } from '@/utils/formValidation'

function handleSubmit() {
  const validationRules = [
    {
      condition: formData.username === '',
      message: '请输入用户名',
      elementId: 'username',
    },
    {
      condition: formData.password === '',
      message: '请输入密码',
      elementId: 'password',
    },
    {
      condition: formData.email === '',
      message: '请输入邮箱',
      elementId: 'email',
    },
  ]

  if (!validateForm(validationRules)) {
    return
  }

  // 验证通过，执行提交逻辑
  submitForm()
}
```

## 高级用法

### 1. 带回调函数的验证

```typescript
import { validateForm } from '@/utils/formValidation'
import { Color } from '@/enums/colorEnum'

const errorColors = reactive({
  username: '',
  password: '',
})

function handleSubmit() {
  const validationRules = [
    {
      condition: formData.username === '',
      message: '请输入用户名',
      elementId: 'username',
      onError: () => {
        // 自定义错误处理逻辑
        console.log('用户名验证失败')
        errorColors.username = Color.red
        // 可以在这里添加其他逻辑，如统计、日志等
      },
    },
    {
      condition: formData.password.length < 6,
      message: '密码至少6位',
      elementId: 'password',
      onError: () => {
        errorColors.password = Color.red
      },
    },
  ]

  if (!validateForm(validationRules)) {
    return
  }

  submitForm()
}
```

## 实际应用示例

### 原始代码（优化前）

```typescript
function handleSubmit() {
  if (formData.gpcType === '') {
    uni.showToast({
      title: '请选择GPC',
      icon: 'none',
      duration: 2000,
    })
    uni.createSelectorQuery().select('#gpcType').boundingClientRect((res) => {
      if (res && !Array.isArray(res)) {
        uni.pageScrollTo({
          scrollTop: res.top,
          duration: 300,
        })
      }
    }).exec()
    return
  }

  if (formData.commonName === '') {
    uni.showToast({
      title: '请输入通用名',
      icon: 'none',
      duration: 2000,
    })
    uni.createSelectorQuery().select('#commonName').boundingClientRect((res) => {
      if (res && !Array.isArray(res)) {
        uni.pageScrollTo({
          scrollTop: res.top,
          duration: 300,
        })
      }
    }).exec()
    return
  }

  // ... 更多重复代码
}
```

### 优化后的代码

```typescript
import { validateForm } from '@/utils/formValidation'

function handleSubmit() {
  const validationRules = [
    {
      condition: formData.gpcType === '',
      message: '请选择GPC',
      elementId: 'gpcType',
    },
    {
      condition: formData.commonName === '',
      message: '请输入通用名',
      elementId: 'commonName',
    },
    {
      condition: formData.goodsName === '',
      message: '请输入产品名称',
      elementId: 'goodsName',
    },
    // ... 更多验证规则
  ]

  if (!validateForm(validationRules)) {
    return
  }

  // 验证通过，执行提交逻辑
  submitForm()
}
```

## 优势

1. **代码复用**: 避免重复的 `uni.showToast` 和 `uni.createSelectorQuery` 代码
2. **统一管理**: 所有验证规则集中在一个数组中，便于维护
3. **灵活扩展**: 支持错误颜色设置、回调函数等扩展功能
4. **类型安全**: 使用 TypeScript 接口确保类型安全
5. **易于测试**: 验证逻辑独立，便于单元测试