const fs = require('node:fs')
const path = require('node:path')
const process = require('node:process')

// 获取命令行参数
const platform = process.argv[2]

// 定义不同平台的配置
const platforms = {
  wx: {
    // 微信：商用条码生成器
    VITE_WX_APPID: 'wxdee9d93adbb330b8',
    VITE_FROM_PLATFORM: 1,
  },
  wx_tiaoMaBang: {
    // 微信：条码邦
    VITE_WX_APPID: 'wx48a2d35bb4b6ce41',
    VITE_FROM_PLATFORM: 4,
  },
  douYin: {
    // 抖音
    VITE_WX_APPID: '',
    VITE_FROM_PLATFORM: 2,
  },
}

// 检查是否提供了有效的平台参数
if (!platform || !platforms[platform]) {
  console.error('请提供有效的平台参数: wx、wx_tiaoMaBang 或 douYin')
  process.exit(1)
}

// 获取所选平台的配置
const config = platforms[platform]

// .env文件路径
const envPath = path.join(__dirname, '..', 'env', '.env')

// 确保env目录存在
const envDir = path.dirname(envPath)
if (!fs.existsSync(envDir)) {
  fs.mkdirSync(envDir, { recursive: true })
  console.log(`创建目录: ${envDir}`)
}

// 读取现有的.env文件内容（如果存在）
let envContent = ''
try {
  envContent = fs.readFileSync(envPath, 'utf8')
}
catch (error) {
  // 如果文件不存在，则创建新文件
  console.log('.env文件不存在，将创建新文件')
}

// 更新配置
Object.keys(config).forEach((key) => {
  const value = config[key]
  // 修改正则表达式以匹配等号两侧可能有空格的情况
  const regex = new RegExp(`^${key}\\s*=\\s*.*`, 'm')

  if (envContent.match(regex)) {
    // 如果配置项已存在，则更新它
    envContent = envContent.replace(regex, `${key}=${value}`)
  }
  else {
    // 如果配置项不存在，则添加它
    envContent += `\n${key}=${value}`
  }
})

// 写入.env文件
fs.writeFileSync(envPath, envContent.trim())

console.log(`已成功配置为${platform}平台：`)
Object.entries(config).forEach(([key, value]) => {
  console.log(`${key}=${value}`)
})
