import type {
  CreateReportOrderParams,
  PriceTempListResData,
} from '@/service/orderApi'

import debounce from 'debounce'
import { storeToRefs } from 'pinia'
import { FromPlatform, ServerType } from '@/enums'
import {
  createReportOrderApi,
  priceTempListApi,

} from '@/service/orderApi'
import { msgModalStore } from '@/store/msgModalStore'
import { orderStore } from '@/store/orderStore'
import { useServiceStore } from '@/store/serviceStore'
import { useUserStore } from '@/store/user'

/**
 * 各服务index页公共方法
 * @param config
 * orderConfirmUrl 订单页面地址
 * paymentSuccessUrl 支付成功后跳转地址，orderConfirmUrl为默认也才生效
 * isMustCustomPrice 是否必须自定义价格，默认false
 * noPriceCallBack 没填价格时，回调用于滚动屏幕到对于位置方便填写。
 */
export function useCreateOrder(config: {
  orderConfirmUrl: string
  paymentSuccessUrl?: string
  isMustCustomPrice?: boolean
  noPriceCallBack?: () => void
}) {
  const useMsgModalStore = msgModalStore()

  const useOrderStore = orderStore()
  const { orderCode, serverType } = storeToRefs(useOrderStore)
  const serviceStore = useServiceStore()
  const { descriptionServiceType } = storeToRefs(serviceStore)
  const userStore = useUserStore()

  const loading = ref(false)
  const isShowStep = ref(true)
  const serveData = ref<PriceTempListResData[]>([])
  const selectServe = ref<number>()
  const customPrice = ref<number | null>()

  onMounted(() => {
    userStore.login().then(() => {
      priceTempListApi({ tempType: serverType.value }).then((res) => {
        serveData.value = res.data
      })
    })
  })

  const handleSubmit = debounce(
    () => {
      if (!selectServe.value) {
        useMsgModalStore.alert({ title: '请选择方案' }).then(() => {
          uni.pageScrollTo({ selector: '#targetElement' })
        })
        return
      }
      if (config.isMustCustomPrice) {
        // 如果必须填价格
        if (!customPrice.value) {
          // 如果没填价格
          useMsgModalStore
            .confirm({
              title: '温馨提示',
              content: '请先联系客服，沟通好价格后，填写金额',
            })
            .then(() => {
              config.noPriceCallBack()
              /*            uni.pageScrollTo({
              selector: '#price',
            }) */
            })
          return
        }
      }
      loading.value = true
      uni.showLoading({ title: '订单提交中...' })
      const params: CreateReportOrderParams = {
        priceTempId: selectServe.value,
        serverType: serverType.value,
        userId: useUserStore().userId,
      }
      // #ifdef MP-WEIXIN
      params.fromTo = import.meta.env.VITE_FROM_PLATFORM
      // #endif
      // #ifdef MP-TOUTIAO
      params.fromTo = FromPlatform.douYin
      // #endif
      if (customPrice.value) {
        params.price = customPrice.value
      }
      // 如果是续展renewalService，还需要判断是否含变更
      if (serverType.value === ServerType.renewalService) {
        // 需要从字符串判断有无“变更”两字
        const d = serveData.value.find(item => item.priceTempId === selectServe.value)
        if (d?.tempName.includes('变更')) {
          params.isHasChange = true
        }
      }
      createReportOrderApi(params)
        .then((res: any) => {
          orderCode.value = res.data.orderCode
          let url = config.orderConfirmUrl
          if (config.paymentSuccessUrl) {
            url
              = `${config.orderConfirmUrl
              }?paymentSuccessUrl=${
                encodeURIComponent(config.paymentSuccessUrl)}`
          }
          loading.value = false
          uni.hideLoading()
          uni.navigateTo({
            url,
          })
        })
        .catch(() => {
          loading.value = false
          uni.hideLoading()
        })
    },
    1000,
    { immediate: true },
  )

  const handleSelect = (evt: any) => {
    // selectServe.value = d.priceTempId
    for (let i = 0; i < serveData.value.length; i++) {
      if (serveData.value[i].priceTempId === Number(evt.detail.value)) {
        selectServe.value = serveData.value[i].priceTempId
        break
      }
    }
  }

  return {
    loading,
    isShowStep,
    customPrice,
    serveData,
    selectServe,
    descriptionServiceType,
    handleSubmit,
    handleSelect,
  }
}
