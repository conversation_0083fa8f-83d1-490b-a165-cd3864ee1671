<script lang="ts" setup>
const props = withDefaults(
  defineProps<{
    price: number
    size: number
    cancel?: boolean // true：划线价格
    thin?: boolean // true：不加粗
    notShowDecimal?: boolean // true：没有小数时，不显示小数
  }>(),
  {
    cancel: false,
    thin: false,
    notShowDecimal: false,
  },
)

const price1 = ref('')
const price2 = ref('')
const showPrice2 = ref(true)

/* const price1 = computed(() => {
  return props.price?.toFixed(2).split('.')[0]
})
const price2 = computed(() => {
  return props.price?.toFixed(2).split('.')[1]
}) */

watch(
  () => props.price,
  () => {
    // p[1]这种用法会报错，所以展开出来
    const [p1, p2] = props.price?.toFixed(2).split('.')
    if (props.notShowDecimal && p2 === '00') {
      showPrice2.value = false
    }
    price1.value = p1
    price2.value = p2
  },
  { immediate: true },
)
</script>

<template>
  <view class="relative flex shrink-0 items-baseline" :class="thin ? '' : 'font-bold'">
    <text :style="{ fontSize: size - 18 < 24 ? 24 : `${size - 18}rpx` }">
      ￥
    </text>
    <text :style="{ fontSize: size < 24 ? 24 : `${size}rpx` }">
      {{ price1 }}
    </text>
    <text v-if="showPrice2" :style="{ fontSize: size - 18 < 24 ? 24 : `${size - 18}rpx` }">
      .{{ price2 }}
    </text>
    <view v-if="cancel" class="f-cancel absolute w-full bg-gray" />
  </view>
</template>

<style lang="scss" scoped>
.f-cancel {
  left: 0;
  top: 50%;
  height: 2rpx;
}
</style>
