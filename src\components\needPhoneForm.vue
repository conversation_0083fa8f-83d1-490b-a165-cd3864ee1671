<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { getTemplateIds } from '@/components/descriptionStr'
import { updateOrderPhoneApi } from '@/service/orderApi'
import { wxparentsUserGetPhoneApi } from '@/service/systemApi'
import { orderStore } from '@/store/orderStore'
import { useUserStore } from '@/store/user'
import { validatePhoneNumber } from '@/utils/tool'

const userStore = useUserStore()
const { localPhone } = storeToRefs(userStore)
const useOrderStore = orderStore()
const { orderCode } = storeToRefs(useOrderStore)
const readOnly = ref(false)
const model = reactive({
  contactPhone: localPhone.value,
})

const rules = {
  contactPhone: [
    {
      required: true,
      message: '请输入联系手机',
      trigger: ['blur'],
    },
    {
      validator: validatePhoneNumber,
      message: '请输入正确的手机号',
      // 触发器可以同时用blur和change
      trigger: ['blur'],
    },
  ],
}

onMounted(() => {
  if (model.contactPhone) {
    readOnly.value = true
  }
})

function getPhoneNumber(e: any) {
  wxparentsUserGetPhoneApi({
    code: e.detail.code,
  }).then((res) => {
    model.contactPhone = res.data
  })
}

function toSavePhone(showToast: boolean = true) {
  if (model.contactPhone === '') {
    uni.showToast({
      icon: 'none',
      title: '手机号不能为空',
    })
  }
  else if (!validatePhoneNumber('', model.contactPhone, () => {})) {
    uni.showToast({
      icon: 'none',
      title: '请输入正确的手机号',
    })
  }
  else {
    updateOrderPhoneApi({
      orderCode: orderCode.value,
      phone: model.contactPhone,
    }).then(() => {
      localPhone.value = model.contactPhone
      if (showToast) {
        uni.showToast({
          icon: 'success',
          title: '已保存',
        })
        readOnly.value = true
      }
    })
  }
}

function handleGetMessage() {
  wx.requestSubscribeMessage({
    tmplIds: getTemplateIds(),
    success(res) {
      console.log('🚀 ~ success ~ res:', res)
    },
    fail(err) {
      console.log('🚀 ~ fail ~ err:', err)
      uni.showToast({
        title: '订阅消息失败',
        icon: 'none',
      })
    },
  })
}

onUnload(() => {
  if (model.contactPhone) {
    toSavePhone(false)
  }
})
</script>

<template>
  <view id="phoneElement" class="mt-3 rd-2 bg-white p-4">
    <view class="f-context mb-2 mt-2 text-sm">
      为保证收到业务办理过程中，接收反馈通知，请留下可联系的手机号：
    </view>
    <view class="flex items-baseline gap-2">
      <view class="shrink-0">
        <text class="text-red-500">
          *
        </text>
        手机号
      </view>
      <up-input
        v-model="model.contactPhone"
        clearable
        :readonly="readOnly"
        :maxlength="11"
        type="number"
        :border="readOnly ? 'none' : 'bottom'"
        placeholder="请输入手机号"
      >
        <template #suffix>
          <text v-if="!readOnly" class="text-xs color-gray">
            {{ model.contactPhone.length }}/11
          </text>
        </template>
      </up-input>
      <template #right>
        <button
          v-show="readOnly"
          class="f-btn-blue flex shrink-0 items-center justify-center px-2 py-1 text-sm text-primary"
          @click="readOnly = false"
        >
          修改手机
        </button>
      </template>
    </view>
    <view
      v-show="!readOnly"
      class="o-bg-primary o-shadow-blue mt-3 flex flex-grow-1 items-center justify-center rd-2 p-3 text-white font-bold"
      @click="toSavePhone"
    >
      保存提交
    </view>
    <view class="mb-2 mt-6">
      为保证业务结果及时通知，请订阅消息：
    </view>
    <button
      v-show="!readOnly"
      class="f-btn-no-style o-bg-primary o-shadow-blue mt-3 flex flex-grow-1 items-center justify-center rd-2 p-3 text-base text-white font-bold"
      @click="handleGetMessage"
    >
      订阅消息
    </button>
  </view>
</template>

<style scoped lang="scss">
.f-btn-blue {
  border: 1px solid rgba(22, 93, 255, 0.3);
  background-color: rgba(22, 93, 255, 0.05);
}

.f-context {
  text-indent: 2em;
}

.f-btn-no-style {
  padding-right: 0;
  padding-left: 0;
  margin-right: 0;
  margin-left: 0;

  &::after {
    border: none;
  }
}
</style>
