<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "物流详情"
  }
}
</route>

<script lang="ts" setup>
import { expressInfoApi } from '@/service/orderApi'

const RESOURCES_URL = import.meta.env.VITE_RESOURCES_URL

const data = ref()
const markers = ref([])
const polyline = ref([])
const isUnfold = ref(false)
const currentLocation = ref({
  latitude: 23.021548,
  longitude: 113.121416,
})
const loading = ref(false)
const mapLoading = ref(true)
const hasData = ref(true)

// 在小程序中直接使用网络地址图片作为标记
// 根据位置使用不同的标记颜色
function getMarkerColor(index, total) {
  if (index === 0)
    return '#4CAF50' // 当前位置绿色
  if (index === total - 1)
    return '#FF0000' // 起始位置红色
  return '#1296DB' // 中间节点蓝色
}

onLoad((options) => {
  getData(options.orderCode)
})

function getData(orderCode: string) {
  loading.value = true
  hasData.value = true
  uni.showLoading({ title: '加载中' })
  expressInfoApi({ orderCode })
    .then((res) => {
      // 检查返回数据是否为空或不包含物流轨迹数据
      if (!res.data || !res.data.list || res.data.list.length === 0) {
        console.log('物流数据为空')
        hasData.value = false
        return
      }

      // 设置物流数据
      data.value = res.data
      console.log('物流数据:', data.value)

      // 处理地图数据
      if (data.value?.list && data.value.list.length > 0) {
        generateMapData()
      }
    })
    .catch((err) => {
      hasData.value = false
      console.error('获取物流信息失败:', err)
    })
    .finally(() => {
      uni.hideLoading()
      loading.value = false
    })
}

function generateMapData() {
  // 显示地图加载状态
  mapLoading.value = true
  uni.showLoading({ title: '加载地图中' })

  // 根据物流信息生成地图标记点和路线
  const logisticsList = data.value.list

  // 标记点
  const markersData = logisticsList
    .filter(item => item.area_location)
    .map((item, index) => {
      const [longitude, latitude] = item.area_location.split(',')
      return {
        id: index,
        latitude: Number.parseFloat(latitude),
        longitude: Number.parseFloat(longitude),
        width: 32,
        height: 32,
        callout: {
          content: `${item.time.split(' ')[0]}`,
          padding: 5,
          borderRadius: 4,
          display: 'ALWAYS',
        },
        // 使用自定义标记
        iconPath: `${RESOURCES_URL}/location.png`,
        label: {
          content: index === 0 ? '当前' : index === logisticsList.length - 1 ? '起点' : '',
          color: getMarkerColor(index, logisticsList.length),
          fontSize: 12,
          anchorX: 16,
          anchorY: -8,
        },
      }
    })

  // 更新当前位置为最新的物流点
  if (markersData.length > 0) {
    currentLocation.value = {
      latitude: markersData[0].latitude,
      longitude: markersData[0].longitude,
    }
  }

  markers.value = markersData

  // 路线
  const points = markersData.map(marker => ({
    latitude: marker.latitude,
    longitude: marker.longitude,
  }))

  polyline.value = [
    {
      points,
      color: '#1296db',
      width: 4,
      arrowLine: true,
    },
  ]

  // 延迟关闭地图加载状态，确保地图渲染完成
  setTimeout(() => {
    uni.hideLoading()
    mapLoading.value = false
  }, 500)
}

function callCourier() {
  if (data.value?.courier_phone) {
    uni.makePhoneCall({
      phoneNumber: data.value.courier_phone,
    })
  }
}

// 地图加载完成事件
function onMapReady() {
  uni.hideLoading()
  mapLoading.value = false
}
</script>

<template>
  <view class="bg-white p-6 text-sm">
    <template v-if="loading">
      <view class="flex items-center justify-center" style="height: 200px">
        <view>数据加载中...</view>
      </view>
    </template>
    <template v-else-if="!hasData">
      <view class="flex flex-col items-center justify-center" style="height: 300px">
        <view class="text-lg text-gray-500">
          暂无快递信息
        </view>
      </view>
    </template>
    <template v-else>
      <view class="mb-4 flex flex-col gap-2 rounded bg-gray-50 p-3">
        <view class="flex justify-between">
          <view class="font-bold">
            {{ data?.company }}
          </view>
          <view class="text-gray-500">
            {{ data?.status_desc }}
          </view>
        </view>
        <view class="flex items-center gap-2">
          <view>单号: {{ data?.no }}</view>
          <view class="text-xs text-gray-500">
            总耗时: {{ data?.take_time }}
          </view>
        </view>
        <view v-if="data?.com_phone" class="flex items-center gap-2">
          <view>快递公司: {{ data?.com_phone }}</view>
          <view v-if="data?.courier_phone" class="text-primary-500" @tap="callCourier">
            快递员: {{ data?.courier_phone }}
          </view>
        </view>
      </view>

      <!-- 地图组件 -->
      <view class="mb-4 w-full">
        <view class="mb-2 font-bold">
          物流轨迹
        </view>
        <view
          v-if="mapLoading"
          class="flex items-center justify-center rd-2 bg-gray-100"
          style="height: 90vw"
        >
          <view>地图加载中...</view>
        </view>
        <map
          v-else
          id="logisticsMap"
          class="map w-full overflow-hidden rd-2"
          :latitude="currentLocation.latitude"
          :longitude="currentLocation.longitude"
          :markers="markers"
          :polyline="polyline"
          :scale="10"
          show-location
          @updated="onMapReady"
        />
      </view>

      <view
        class="relative mt-4 overflow-hidden"
        :style="isUnfold ? 'height: auto' : 'height: 80vw'"
      >
        <up-steps :active="0" dot direction="column">
          <up-steps-item
            v-for="(item, index) in data?.list"
            :key="index"
            :title="item.time"
            :desc="item.context"
          />
        </up-steps>
        <view
          v-if="!isUnfold"
          class="f-list-hidden absolute bottom-0 left-0 z-1 w-full"
          @click="isUnfold = true"
        >
          <view class="left absolute bottom-0 w-full text-center text-xs">
            展开
          </view>
        </view>
      </view>
      <view class="flex py-4">
        <up-icon class="shrink-0" name="map" size="20" />
        <view class="flex-1">
          <view class="text-sm">
            <text class="pr-1">
              送至
            </text>
            {{ data?.addressDetail }}
          </view>
          <view class="flex">
            <view class="text-xs text-gray-500">
              {{ data?.contact }}
            </view>
            <view class="text-xs text-gray-500">
              {{ data?.contactPhone }}
            </view>
          </view>
        </view>
      </view>
    </template>
  </view>
</template>

<style lang="scss" scoped>
.map {
  height: 70vw;
}
.f-list-hidden {
  height: 16vw;
  background: linear-gradient(rgba(255, 255, 255, 0) 0%, #fff 70%);
}
</style>
