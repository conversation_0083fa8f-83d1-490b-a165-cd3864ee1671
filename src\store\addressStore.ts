import { defineStore } from 'pinia'
import { ref } from 'vue'

export const addressStore = defineStore('addressStore', () => {
  const selectAddressId = ref(0)
  const addressId = ref(0)
  const addressData = ref<{
    addressDetail: string
    district: string
    isDefault: boolean
    phone: string
    realName: string
  }>({
    addressDetail: '',
    district: '',
    isDefault: false,
    phone: '',
    realName: '',
  })

  return { addressData, addressId, selectAddressId }
})
