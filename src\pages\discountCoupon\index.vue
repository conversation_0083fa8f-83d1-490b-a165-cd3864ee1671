<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page -->
<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "红包卡券",
    "enablePullDownRefresh": true,
    "backgroundColor":"#f0f3f8"
  }
}
</route>

<script lang="ts" setup>
import type { CouponPageResData } from '@/service/orderApi'
import { storeToRefs } from 'pinia'
import CouponCard from '@/components/Price/CouponCard.vue'
import { ServerType } from '@/enums'
import { OrderDirection } from '@/enums/httpEnum'
import { couponPageApi } from '@/service/orderApi'
import { orderStore } from '@/store/orderStore'
import { useUserStore } from '@/store/user'

const userStore = useUserStore()
const { userId } = storeToRefs(userStore)
const useOrderStore = orderStore()
const {
  vendorCode,
  totalPrice,
  isSelectedCoupon,
  serverType,
  couponId,
  couponType,
  discount,
  couponPrice,
} = storeToRefs(useOrderStore)

const toSelect = ref(false)
const listTimeOut = ref([])
const list = ref<CouponPageResData[]>([])
const makeFilmHasOtherServer = ref('')

// 店内码用的是条码制作优惠券
const mixedServerType = computed(() => {
  if (serverType.value === ServerType.storeCode) {
    return ServerType.makeFilm
  }
  else {
    return serverType.value
  }
})

/**
 * 禁止选择优惠券，返回true代表禁选
 * 支付时筛选优惠券时，筛选厂商识别代码，不满足时灰色
 * @param data
 * @param openDisabled 是否开启可选判断
 */
function canSelect(data: any, openDisabled: boolean) {
  // 是否开启禁止判断
  // if (!openDisabled) return false
  if (openDisabled) {
    // 是否过期
    if (data.isTimeOut === 1)
      return false
    // 服务类型是否相同
    if (data.serverType !== mixedServerType.value)
      return false
    // 厂商识别码是否符合，服务类型，1：条码制作，2：信息上报
    if (
      mixedServerType.value === ServerType.makeFilm
      && data.vendorCode !== null
      && data.vendorCode !== vendorCode.value
    ) {
      return false
    }
    if (data.serverType === ServerType.makeFilm) {
      if (makeFilmHasOtherServer.value === 'true') {
        if (!data.isStandardCode)
          return false
      }
      else if (makeFilmHasOtherServer.value === 'false') {
        if (data.isStandardCode)
          return false
      }
    }
    // 券最低使用价格
    if (data.minUsagePrice !== null && totalPrice.value < data.minUsagePrice)
      return false
    return true
  }
  return false
}

function getCouponList() {
  couponPageApi({
    isUsed: 0,
    isTimeOut: 0,
    groupBy: '',
    needTotalCount: true,
    orderBy: 'grantDate',
    orderDirection: OrderDirection.desc,
    pageIndex: 1,
    pageSize: 100000,
    userId: userId.value,
  }).then((res) => {
    list.value = res.data
  })
}

function getTimeOutCouponList() {
  couponPageApi({
    isUsed: 0,
    isTimeOut: 1,
    groupBy: '',
    needTotalCount: true,
    orderBy: 'grantDate',
    orderDirection: OrderDirection.desc,
    pageIndex: 1,
    pageSize: 10, // 过期的不再需要显示更多了
    userId: userId.value,
  }).then((res) => {
    listTimeOut.value = res.data
  })
}

onPullDownRefresh(() => {
  list.value = []
  listTimeOut.value = []
  Promise.all([getCouponList(), getTimeOutCouponList()]).finally(() => {
    uni.stopPullDownRefresh()
  })
})

function handleSelect(data) {
  if (canSelect(data, toSelect.value)) {
    couponId.value = data.couponId
    couponType.value = data.couponType
    discount.value = data.discount
    couponPrice.value = data.couponPrice
    isSelectedCoupon.value = true
    uni.navigateBack()
  }
}

onLoad((option) => {
  // 开启选择模式
  toSelect.value = option.toSelect === 'true'
  if (option.makeFilmHasOtherServer) {
    // makeFilmHasOtherServer有三种，字符串‘true’表示含服务，'false'表示不含服务，''则表示不是条码方面
    makeFilmHasOtherServer.value = option.makeFilmHasOtherServer
  }
  getCouponList()
  getTimeOutCouponList()
})
</script>

<template>
  <view class="px-3 pb-10 pt-3">
    <view v-if="toSelect" class="py-3">
      请选择优惠券：
    </view>
    <view
      v-for="item in list"
      :key="item.couponId"
      :class="!toSelect || canSelect(item, toSelect) ? 'bg-white' : 'o-bg-white-disable'"
      class="relative mb-3 overflow-hidden rd-2 p-4"
      @click="handleSelect(item)"
    >
      <coupon-card :data="item" :disabled="toSelect && !canSelect(item, toSelect)" />
    </view>
    <up-empty
      v-if="list?.length === 0"
      icon="https://wx.gs1helper.com/images/common/content.png"
      text="暂无优惠券"
    />
    <view v-if="listTimeOut?.length > 0">
      <view class="py-3">
        已过期优惠券：
      </view>
      <view
        v-for="item in listTimeOut"
        :key="item.couponId"
        class="relative mb-3 overflow-hidden rd-2 bg-white p-4"
      >
        <coupon-card :data="item" :disabled="true" />
      </view>
    </view>
    <view class="o-color-aid mt-4 w-full text-center text-sm">
      - 已经到底了 -
    </view>
  </view>
</template>

<style lang="scss" scoped></style>
