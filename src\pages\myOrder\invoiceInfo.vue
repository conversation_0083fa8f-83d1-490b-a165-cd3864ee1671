<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page -->
<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "开票资料"
  }
}
</route>

<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import PaidDisclaimer from '@/components/agreement/paidDisclaimer.vue'
import { useInvoiceInfoHook } from '@/components/invoice/invoiceInfoHook'
import { Article } from '@/enums'
import { OrderDirection } from '@/enums/httpEnum'
import {
  invoiceTempCreateApi,
  invoiceTempleApi,
  invoiceTempLoadApi,
  invoiceTempUpdateApi,
} from '@/service/invoiceTempleApi'
import { downloadOrderInvoiceApi } from '@/service/orderApi'
import { invoiceOrderStore } from '@/store/invoiceOrderStore'
import { msgModalStore } from '@/store/msgModalStore'
import { useUserStore } from '@/store/user'

const useMsgModalStore = msgModalStore()

const useInvoiceOrderStore = invoiceOrderStore()
const { orderObjList, orderSuccess } = storeToRefs(useInvoiceOrderStore)

const agree = ref(false)
let isAdd = false
const price = ref('0')
const length = ref('0')
const userStore = useUserStore()
const isModalShow = ref(false)
const loading = ref(false)
const { userId, defaultInvoiceTempId } = storeToRefs(userStore)

onLoad((option: { length: string, price: string }) => {
  // console.log(option)
  price.value = option.price
  length.value = option.length
})

onShow(() => {
  // getInvoiceTemp()
  // 隐藏错误提示，获取不了默认发票资料，则读第一位list内容，如果第一位list内容为空，则创建
  getInvoiceTemp(true)
    .then(() => {
      isAdd = false
    })
    .catch(() => {
      getInvoiceFistId()
        .then(() => {
          // 编辑
          isAdd = false
          getInvoiceTemp()
        })
        .catch(() => {
          // 创建
          isAdd = true
        })
    })
})

const {
  model,
  form,
  validatePhoneNumber,
  validateMail,
  validateIdentificationNumber,
  validateBankCode,
} = useInvoiceInfoHook()

const rules = {
  companyName: [{ required: true, message: '请输入发票抬头', trigger: ['blur'] }],
  creditCode: [
    {
      required: true,
      validator: validateIdentificationNumber,
      message: '请输入正确纳税人识别号',
      trigger: ['blur'],
    },
  ],
  email: [
    {
      required: true,
      message: '请输入邮箱地址',
      trigger: ['blur'],
    },
    {
      validator: (rule, value, callback) => {
        return uni.$u.test.email(value)
      },
      message: '请输入正确的邮箱地址',
      // 触发器可以同时用blur和change
      trigger: ['blur'],
    },
  ],
  contact: [{ required: true, message: '请输入联系人名称', trigger: ['blur'] }],
  contactPhone: [
    {
      required: true,
      message: '请输入联系手机',
      trigger: ['blur'],
    },
    {
      validator: validatePhoneNumber,
      message: '请输入正确的手机号',
      // 触发器可以同时用blur和change
      trigger: ['blur'],
    },
  ],
  bankCode: [
    {
      required: false,
      validator: validateBankCode,
      message: '请输入正确银行帐号',
      trigger: ['blur'],
    },
  ],
}

function getInvoiceTemp(hideErrorToast = false) {
  return new Promise((resolve, reject) => {
    invoiceTempLoadApi(
      {
        invoiceTempId: defaultInvoiceTempId.value,
      },
      hideErrorToast,
    )
      .then((res: any) => {
        const d = res.data
        model.contact = d.contact
        model.contactPhone = d.contactPhone
        model.companyName = d.companyName
        model.creditCode = d.creditCode
        model.email = d.email
        model.bank = d.bank
        model.bankCode = d.bankCode
        model.phone = d.phone
        model.address = d.address
        resolve(true)
      })
      .catch((error) => {
        reject(error)
      })
  })
}

function getInvoiceFistId() {
  return new Promise((resolve, reject) => {
    invoiceTempleApi({
      companyName: '',
      creditCode: '',
      userId: userId.value,
      groupBy: '',
      needTotalCount: true,
      orderBy: 'invoiceTempId',
      orderDirection: OrderDirection.desc,
      pageIndex: 1,
      pageSize: 100000,
    })
      .then((res: any) => {
        if (res.data.length > 0) {
          defaultInvoiceTempId.value = res.data[0].invoiceTempId
          resolve(true)
        }
        else {
          reject(res)
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

async function createInvoiceTemp() {
  try {
    const res = await invoiceTempCreateApi({
      address: model.address,
      bank: model.bank,
      bankCode: model.bankCode,
      companyName: model.companyName,
      contact: model.contact,
      contactPhone: model.contactPhone,
      creditCode: model.creditCode,
      email: model.email,
      phone: model.phone,
      userId: userId.value,
    })
    defaultInvoiceTempId.value = res.data.invoiceTempId
    return [res.data, null]
  }
  catch (error) {
    console.error('创建发票模板失败:', error)
    return [null, error]
  }
}

async function updateInvoiceTemp() {
  try {
    const response = await invoiceTempUpdateApi({
      address: model.address,
      bank: model.bank,
      bankCode: model.bankCode,
      companyName: model.companyName,
      contact: model.contact,
      contactPhone: model.contactPhone,
      creditCode: model.creditCode,
      email: model.email,
      phone: model.phone,
      userId: userId.value,
      invoiceTempId: defaultInvoiceTempId.value,
    })
    return [response, null]
  }
  catch (error) {
    console.error('更新发票模板失败:', error)
    return [null, error]
  }
}

function downloadInvoice() {
  downloadOrderInvoiceApi({
    address: model.address,
    bank: model.bank,
    bankCode: model.bankCode,
    companyName: model.companyName,
    contact: model.contact,
    contactPhone: model.contactPhone,
    creditCode: model.creditCode,
    downloadType: 2,
    email: model.email,
    phone: model.phone,
    invoiceTempId: defaultInvoiceTempId.value,
    userId: userId.value,
    orderList: orderObjList.value,
  }).then(() => {
    useMsgModalStore
      .alert({
        title: '申请成功',
        content: '发票将在1~2个工作日内发送到您邮箱。',
        closeOnClickOverlay: false,
      })
      .then(() => {
        loading.value = false
        orderSuccess.value = true
        uni.navigateBack()
      })
      .catch(() => {
        loading.value = false
      })
  })
}

async function handleSubmit() {
  if (!orderSuccess.value && !loading.value) {
    loading.value = true
    form.value
      .validate()
      .then(async (valid) => {
        if (valid) {
          if (agree.value) {
            let error: null
            if (isAdd) {
              const [d, errorCreate] = await createInvoiceTemp()
              error = errorCreate
              // 将新创建的模板id赋值给默认模板id，本地存储
              defaultInvoiceTempId.value = d.invoiceTempId
            }
            else {
              const [, errorUpdate] = await updateInvoiceTemp()
              error = errorUpdate
            }
            if (!error) {
              downloadInvoice()
            }
            else {
              loading.value = false
            }
          }
          else {
            loading.value = false
            useMsgModalStore
              .confirm({
                title: '温馨提示',
                content: '请先勾选已阅读并同意《用户隐私协议》',
              })
              .then(() => {
                uni.pageScrollTo({
                  selector: '#agreeElement',
                })
              })
          }
        }
        else {
          loading.value = false
        }
      })
      .catch((error) => {
        console.log(error, 'error')
        loading.value = false
      })
  }
}

function toSelectInvoiceTemplate() {
  uni.navigateTo({
    url: '/pages/invoiceTemple/index?toSelect=true',
  })
}

function handleModalOk() {
  agree.value = true
  isModalShow.value = false
}
</script>

<template>
  <up-modal
    :show="isModalShow"
    confirm-text="同意"
    show-cancel-button
    close-on-click-overlay
    selector="privacy-policy"
    @confirm="handleModalOk"
    @cancel="isModalShow = false"
    @close="isModalShow = false"
  >
    <PaidDisclaimer v-if="isModalShow" :article-ids="[Article.privacy]" />
  </up-modal>
  <view class="flex">
    <view class="o-bg-primary o-shadow-blue p-1.5" />
    <view class="flex-grow-1 bg-white p-4">
      <view>
        已选
        <text class="font-bold">
          {{ length }}
        </text>
        张订单，
      </view>
      <view>
        合计
        <text class="font-bold">
          {{ price }}
        </text>
        元。
      </view>
    </view>
  </view>
  <view class="o-color-aid mt-3 flex items-center justify-end pr-4 text-sm">
    <view @click="toSelectInvoiceTemplate">
      选择其他模板
    </view>
    <view class="px-1">
      <up-icon name="arrow-right" size="14" />
    </view>
  </view>
  <view class="px-4 pb-4 pt-2">
    <view class="overflow-hidden rd-2 bg-white pl-4">
      <up-form ref="form" :model="model" :rules="rules" class="ml-2 mt-2" label-width="100">
        <up-form-item required label="发票抬头" prop="companyName">
          <up-input
            v-model="model.companyName"
            clearable
            border="bottom"
            placeholder="企业名称"
          />
        </up-form-item>
        <up-form-item required label="纳税人识别号" prop="creditCode">
          <up-input
            v-model="model.creditCode"
            clearable
            :maxlength="20"
            border="bottom"
            placeholder=""
          />
        </up-form-item>
        <up-form-item required label="电子邮箱" prop="email">
          <up-input
            v-model="model.email"
            clearable
            border="bottom"
            placeholder="用于接收电子发票"
          />
        </up-form-item>
        <up-form-item required label="联系人" prop="contact">
          <up-input v-model="model.contact" clearable border="bottom" placeholder="" />
        </up-form-item>
        <up-form-item required label="联系手机号" prop="contactPhone">
          <up-input
            v-model="model.contactPhone"
            clearable
            :maxlength="11"
            border="bottom"
            placeholder=""
          >
            <template #suffix>
              <text class="text-xs color-gray">
                {{ model.contactPhone.length }}/11
              </text>
            </template>
          </up-input>
        </up-form-item>
        <up-form-item label="开户银行" prop="bank">
          <up-input v-model="model.bank" clearable border="bottom" placeholder="选填" />
        </up-form-item>
        <up-form-item label="银行账号" prop="bankCode">
          <up-input
            v-model="model.bankCode"
            clearable
            border="bottom"
            placeholder="选填"
          />
        </up-form-item>
        <up-form-item label="注册地址" prop="address">
          <up-input v-model="model.address" clearable border="bottom" placeholder="选填" />
        </up-form-item>
        <up-form-item label="注册电话" prop="phone">
          <up-input v-model="model.phone" clearable border="bottom" placeholder="选填" />
        </up-form-item>
      </up-form>
    </view>
  </view>
  <view
    id="agreeElement"
    class="flex items-center justify-center pb-20 text-xs"
    @click="agree = !agree"
  >
    <up-checkbox
      v-model:checked="agree"
      used-alone
      label-size="12"
      size="14"
      label="我已阅读并同意"
    />
    <view class="text-primary" @click.stop="isModalShow = true">
      《用户隐私协议》
    </view>
  </view>
  <view class="fixed bottom-0 left-0 z-10 box-border w-full p-4">
    <view
      :class="orderSuccess || loading ? 'o-bg-primary-disable' : 'o-bg-primary o-shadow-blue'"
      class="flex flex-grow-1 items-center justify-center rd-2 p-3 text-white font-bold"
      @click="handleSubmit"
    >
      提交开票信息
    </view>
  </view>
</template>

<style lang="scss" scoped></style>
