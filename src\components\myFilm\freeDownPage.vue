<script lang="ts" setup>
import type {
  BarCodePageRes,
} from '@/service/barcodePageApi'
import debounce from 'debounce'
import { storeToRefs } from 'pinia'

import { BarType, ReportStatus } from '@/enums'
import { Color } from '@/enums/colorEnum'
import { OrderDirection } from '@/enums/httpEnum'
import BackTop from '@/pages/about/components/backTop/backTop.vue'
import { useScrollViewBackTop } from '@/pages/about/components/backTop/useScrollViewBackTop'
import {
  barcodePageApi,
  downloadBarCodeApi,
  sizeListApi,
  vendorCodeListApi,
} from '@/service/barcodePageApi'
import { msgModalStore } from '@/store/msgModalStore'
import { useUserStore } from '@/store/user'
import { validateMail } from '@/utils/tool'

const userStore = useUserStore()
const { userId, downloadEmail } = storeToRefs(userStore)

const useMsgModalStore = msgModalStore()

const barCode = ref('')
const coefficient = ref('')
const isShowFilter = ref(false)
const isModalShow = ref(false)
const isFilter = ref(false)
const isSelectAll = ref(false)
const isShowVendorCodePick = ref(false)
const isShowSizePick = ref(false)
const isLoadingSendOrder = ref(false)
const isShowUnusedCode = ref(false)
const barTypeArr = ref<string[]>([])
const vendorCode = ref('')
const selectValue = ref<any>([])
// 当前下拉刷新状态，true 表示下拉刷新已经被触发，false 表示下拉刷新未被触发
const refreshTriggered = ref(false)
const page = ref(1)
const pageSize = 500
const formModel = reactive({
  email: downloadEmail.value,
})

interface CheckGroupChangeType {
  type: string
  timeStamp: number
  target: {
    id: string
    dataset: any
    offsetLeft: number
    offsetTop: number
    value: string[]
  }
  currentTarget: {
    id: string
    dataset: any
    offsetLeft: number
    offsetTop: number
  }
  detail: {
    value: string[]
  }
  touches: any[]
  changedTouches: any[]
}

const { scrollTop, flag, getScroll, getToTop } = useScrollViewBackTop()

function getBarType(arr) {
  if (arr.length > 1) {
    return ''
  }
  else {
    if (arr.includes(BarType.EAN13)) {
      return BarType.EAN13
    }
    else if (arr.includes(BarType.ITF14)) {
      return BarType.ITF14
    }
  }
  return ''
}

function arrToStr(d: any) {
  if (typeof d !== 'string') {
    return ''
  }
  else {
    return d
  }
}

function isShowCoefficientClear(d: any) {
  if (d === '') {
    return false
  }
  else {
    return !Array.isArray(d)
  }
}

const { loading, error, data, pageIndex, totalCount, totalPages, run } = useRequest<BarCodePageRes>(
  () =>
    barcodePageApi({
      barCode: barCode.value,
      barType: getBarType(barTypeArr.value),
      size: Number(coefficient.value),
      userId: userId.value,
      vendorCode: arrToStr(vendorCode.value),
      groupBy: '',
      needTotalCount: true,
      orderBy: 'barCode',
      orderDirection: OrderDirection.asc,
      pageIndex: page.value,
      pageSize,
      // TODO: 添加isShowUnusedCode
    }),
)

function transformSize(d: any) {
  const r = d.map((item: any) => item.size)
  r.splice(r.indexOf(null), 1)
  // 将r从小到大排序
  return [r.sort((a, b) => a - b)]
}

function transformVendorCode(d: any) {
  const r = d.map((item: any) => item.vendorCode)
  // 去掉r中为null的项
  r.splice(r.indexOf(null), 1)
  return [r.sort((a, b) => a - b)]
}

const {
  loading: sizeLoading,
  data: sizeData,
  run: sizeRun,
} = useRequest(() => sizeListApi({ userId: userId.value }), { transform: transformSize })

const {
  loading: vendorCodeLoading,
  data: vendorCodeData,
  run: vendorCodeRun,
} = useRequest(() => vendorCodeListApi({ userId: userId.value }), {
  transform: transformVendorCode,
})

watch(isSelectAll, () => {
  if (isSelectAll.value) {
    selectValue.value = data.value.map(item => item.barCodeId)
  }
  else {
    selectValue.value = []
  }
})

function handleSubmit() {
  if (selectValue.value.length > 0) {
    isModalShow.value = true
  }
}

function handleSearch() {
  isSelectAll.value = false
  selectValue.value = []
  run()
  isShowFilter.value = false
}

function clearVendorCode() {
  vendorCode.value = ''
  isShowVendorCodePick.value = false
}

function clearCoefficient() {
  coefficient.value = ''
  isShowSizePick.value = false
}

function handleReset() {
  selectValue.value = []
  barCode.value = ''
  barTypeArr.value = []
  vendorCode.value = ''
  coefficient.value = ''
  run()
  isShowFilter.value = false
}

watch([coefficient, vendorCode, barTypeArr], () => {
  isFilter.value
    = coefficient.value !== '' || vendorCode.value !== '' || barTypeArr.value.length > 0
})

function handlePageChange() {
  isSelectAll.value = false
  selectValue.value = []
  run()
}

function handleModalOk() {
  if (isLoadingSendOrder.value)
    return
  if (formModel.email === '') {
    uni.showToast({
      icon: 'none',
      title: '请输入邮箱地址',
    })
  }
  else if (!validateMail(formModel.email)) {
    uni.showToast({
      icon: 'none',
      title: '请输入正确的邮箱地址',
    })
  }
  else {
    isLoadingSendOrder.value = true
    uni.showLoading({ title: '提交申请中...' })
    downloadBarCodeApi({
      barCodeIdList: selectValue.value,
      downloadType: 2,
      email: formModel.email,
      userId: userId.value,
    })
      .then(() => {
        downloadEmail.value = formModel.email
        uni.hideLoading()
        isModalShow.value = false
        useMsgModalStore.alert({
          title: '已安排发送到邮箱',
          content:
            '鉴于条码制作需要时间，且邮件收发速度受网络影响，如果长时间没有收到邮件，请再提交申请，或咨询客服。谢谢！',
        })
      })
      .catch(() => {
        uni.hideLoading()
      })
      .finally(() => {
        isLoadingSendOrder.value = false
      })
  }
}

function vendorCodeConfirm(e: any) {
  vendorCode.value = e.value[0]
  isShowVendorCodePick.value = false
}

function showVendorCodePick() {
  isShowVendorCodePick.value = true
  vendorCodeRun()
}

function sizeConfirm(e: any) {
  coefficient.value = e.value[0]
  isShowSizePick.value = false
}

function showSizePick() {
  isShowSizePick.value = true
  sizeRun()
}
function handlePreviousPage() {
  if (page.value !== 1) {
    page.value--
    handlePageChange()
  }
}
function handleNextPage() {
  if (page.value >= totalPages.value) {
    page.value = totalPages.value
  }
  else {
    page.value++
    handlePageChange()
  }
}

function barTypeCheckboxChange(e?: CheckGroupChangeType) {
  if (e?.detail?.value) {
    barTypeArr.value = e.detail.value
  }
}

function isSelectAllCheckboxChange(e?: CheckGroupChangeType) {
  if (e?.detail?.value !== undefined) {
    isSelectAll.value = e.detail.value.length !== 0
  }
}

function selectValueCheckboxChange(e?: CheckGroupChangeType) {
  if (e?.detail?.value) {
    selectValue.value = e.detail.value.map(item => Number(item))
  }
}

function onRefresh() {
  if (refreshTriggered.value)
    return
  page.value = 1
  refreshTriggered.value = true
  run().finally(() => {
    refreshTriggered.value = false
  })
}

const handleInput = debounce(() => {
  handleSearch()
}, 800)

function toCreateInfoReport(barCode: string) {
  uni.navigateTo({
    url: `/pages/infoReportMg/infoReportDetailFromPage?barCode=${barCode}`,
  })
}

function handleInfoReportBtnClick(barCode: string, reportStatus: ReportStatus | null) {
  if ([ReportStatus.reporting, ReportStatus.done].includes(reportStatus)) {
    uni.navigateTo({
      url: `/pages/infoReportMg/infoReportDetailPage?barCode=${barCode}`,
    })
  }
  else {
    uni.navigateTo({
      url: `/pages/infoReportMg/infoReportDetailFromPage?action=create&barCode=${barCode}`,
    })
  }
}

function getReportStatusColor(reportStatus: ReportStatus | null) {
  switch (reportStatus) {
    case ReportStatus.reporting:
      return 'text-primary'
    case ReportStatus.done:
      return 'text-emerald-500'
    case ReportStatus.notReport:
      return 'text-gray'
    default:
      return 'text-gray'
  }
}

function getReportStatusStr(reportStatus: ReportStatus | null) {
  switch (reportStatus) {
    case ReportStatus.reporting:
      return '通报中'
    case ReportStatus.done:
      return '已使用'
    case ReportStatus.notReport:
      return '未使用'
    default:
      return '未使用'
  }
}

onMounted(() => {
  userStore.login().then(() => {
    run()
  })
})
</script>

<template>
  <view>
    <up-modal
      :show="isModalShow" close-on-click-overlay show-cancel-button title="条码将以邮件形式发送"
      @cancel="isModalShow = false" @close="isModalShow = false" @confirm="handleModalOk"
    >
      <up-input v-model="formModel.email" border="bottom" placeholder="请输入接收邮箱" />
    </up-modal>
    <view class="relative z-1 bg-white py-2">
      <view class="f-bottom-btn fixed left-0 z-10 box-border w-full p-4">
        <view
          :class="selectValue.length > 0 ? 'o-bg-primary o-shadow-blue' : 'o-bg-primary-disable'"
          class="flex flex-grow-1 items-center justify-center rd-2 p-3 text-white font-bold" @click="handleSubmit"
        >
          批量下载
        </view>
      </view>
      <view v-if="isShowFilter" class="f-overlay" @click="isShowFilter = false" />
      <view class="o-bg-no mx-4 flex items-center gap-2 py-1 pl-4 pr-3" style="border-radius: 2rem">
        <up-icon :color="Color.gray" name="search" size="20" />
        <up-input
          v-model="barCode" :maxlength="14" border="none" class="o-bg-transparent grow" clearable
          placeholder="条码编码" type="number" @change="handleInput"
        />
        <view
          :class="isFilter ? 'text-primary' : 'o-color-aid'" class="flex items-center gap-1 text-sm"
          @click="isShowFilter = !isShowFilter"
        >
          <view class="shrink-0">
            筛选
          </view>
          <view style="min-width: 1.5rem">
            <up-icon v-if="isShowFilter" name="arrow-up" size="14" />
            <up-icon v-else name="arrow-down" size="14" />
          </view>
        </view>
      </view>
      <view class="relative">
        <view v-if="isShowFilter" class="absolute left-0 z-10 box-border w-full bg-white p-4">
          <view class="f-form-item">
            <view class="f-label">
              条码类型
            </view>
            <!--          <up-checkbox-group v-model="barTypeArr" class="grow-1">
            <up-checkbox :name="BarType.EAN13" label="EAN-13"></up-checkbox>
            <text class="px-2"></text>
            <up-checkbox :name="BarType.ITF14" label="ITF-14"></up-checkbox>
          </up-checkbox-group> -->
            <checkbox-group class="flex grow-1" @change="barTypeCheckboxChange">
              <label class="mr-2 flex items-center">
                <checkbox
                  :color="Color.primary" style="transform: scale(0.7)" :value="BarType.EAN13"
                  :checked="barTypeArr.includes(BarType.EAN13)"
                />
                <text>EAN-13</text>
              </label>
              <label class="flex items-center">
                <checkbox
                  :color="Color.primary" style="transform: scale(0.7)" :value="BarType.ITF14"
                  :checked="barTypeArr.includes(BarType.ITF14)"
                />
                <text>ITF-14</text>
              </label>
            </checkbox-group>
          </view>
          <view class="f-form-item" @click="showVendorCodePick">
            <view class="f-label">
              厂商识别码
            </view>
            <up-input
              v-model="vendorCode" border="bottom" class="grow-1" disabled disabled-color="#fff"
              placeholder="请选择厂商识别码"
            />
            <template #right>
              <up-icon name="arrow-right" size="14" />
            </template>
          </view>
          <up-picker
            :columns="vendorCodeData" :loading="vendorCodeLoading" :show="isShowVendorCodePick"
            :confirm-color="Color.primary" cancel-text="清空" close-on-click-overlay title="厂商识别码"
            @cancel="clearVendorCode" @close="isShowVendorCodePick = false" @confirm="vendorCodeConfirm"
          />
          <view class="f-form-item" @click="showSizePick">
            <view class="f-label">
              放大系数
            </view>
            <up-input
              v-model="coefficient" border="bottom" class="grow-1" disabled disabled-color="#ffffff"
              placeholder="请选择放大系数"
            />
            <template #right>
              <up-icon name="arrow-right" size="14" />
            </template>
          </view>
          <up-picker
            :columns="sizeData" :loading="sizeLoading" :show="isShowSizePick" :confirm-color="Color.primary"
            cancel-text="清空" close-on-click-overlay title="放大系数" @cancel="clearCoefficient"
            @close="isShowSizePick = false" @confirm="sizeConfirm"
          />
          <view class="mt-3 flex gap-2">
            <view class="o-border flex flex-grow-1 items-center justify-center rd-2 p-2" @click="handleReset">
              重置
            </view>
            <view
              class="o-bg-primary o-shadow-blue flex flex-grow-2 items-center justify-center rd-2 p-2 text-white"
              @click="handleSearch"
            >
              筛选
            </view>
          </view>
        </view>
      </view>
      <view class="mx-4 mt-2 flex justify-between">
        <checkbox-group @change="isSelectAllCheckboxChange">
          <label>
            <checkbox :color="Color.primary" style="transform: scale(0.7)" value="0" :checked="isSelectAll" />
            全选本页条码
          </label>
        </checkbox-group>
        <view class="flex items-center space-x-1">
          <text :class="!isShowUnusedCode && 'text-gray'">只显示未使用</text>
          <up-switch v-model="isShowUnusedCode" @change="handleSearch" />
        </view>
      </view>
    </view>
    <scroll-view
      :enable-flex="true" :refresher-enabled="true" :refresher-triggered="refreshTriggered"
      :scroll-top="scrollTop" :scroll-with-animation="true" class="o-bg-no f-scroll-box relative" :scroll-y="true"
      @refresherrefresh="onRefresh" @scroll="getScroll"
    >
      <checkbox-group @change="selectValueCheckboxChange">
        <label
          v-for="item in data" :key="item.barCodeId" class="f-checkbox flex items-center"
          :class="selectValue.includes(item.barCodeId) ? 'bg-white py-2 px-4' : 'py-2 px-4'"
        >
          <checkbox
            :color="Color.primary" style="transform: scale(0.7)" :value="item.barCodeId.toString()"
            :checked="selectValue.includes(item.barCodeId)"
          />
          <view class="flex-1">
            <view class="flex items-baseline space-x-2">
              <view class="o-barcode-gray-card rd-1">
                {{ item.barCode }}
              </view>
              <view class="text-xs" :class="getReportStatusColor(item.reportStatus)">
                {{ getReportStatusStr(item.reportStatus) }}
              </view>
            </view>
            <view class="mt-1 text-xs text-gray-500">
              放大系数：{{ item.size }}
            </view>
          </view>
          <view
            class="center shrink-0 rd-2 bg-gray-200 px-2 py-1 text-xs text-primary space-x-1"
            @click.stop="handleInfoReportBtnClick(item.barCode, item.reportStatus)"
          >
            <view>
              {{ [ReportStatus.reporting, ReportStatus.done].includes(item.reportStatus) ? '查看通报产品' : '新建通报' }}
            </view>
            <up-icon name="arrow-right" size="11" :color="Color.gray" class="shrink-0" />
          </view>
        </label>
      </checkbox-group>
      <template v-if="!(data?.length > 0)">
        <up-loading-icon v-if="loading" class="py-10" mode="circle" />
        <up-empty
          v-if="error || loading === false" icon="https://wx.gs1helper.com/images/common/search.png"
          text="当前搜索无结果"
        />
      </template>

      <view v-if="totalPages > 1" class="flex items-center justify-center gap-4 p-4 text-sm">
        <up-icon
          :color="page === 1 ? Color.disabledColor : Color.inputColor" class="p-4" name="arrow-left" size="14"
          @click="handlePreviousPage"
        />
        <view>{{ page }} / {{ totalPages }}</view>
        <up-icon
          :color="page >= totalPages ? Color.disabledColor : Color.inputColor" class="p-4" name="arrow-right"
          size="14" @click="handleNextPage"
        />
      </view>
      <view class="py-4" />
      <view class="o-pb" />
    </scroll-view>
    <back-top v-if="flag" @tap="getToTop" />
  </view>
</template>

<style lang="scss" scoped>
.f-form-item {
  @apply flex items-center;
}

.f-label {
  @apply mr-4;
  min-width: 5rem;
}

.f-checkbox {
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.f-scroll-box {
  height: calc(100vh - 8.1rem);
}

.f-table-t {
  width: 310rpx;
}

.f-table-c {
  width: 150rpx;
}

.f-overlay {
  position: absolute;
  top: 4rem;
  left: 0;
  z-index: 10;
  width: 100%;
  height: calc(100vh - calc(44px + env(safe-area-inset-top)));
  background-color: rgba(0, 0, 0, 0.7);
  transition-duration: 300ms;
}

.f-bottom-btn {
  bottom: calc(130rpx + env(safe-area-inset-bottom));
}
</style>
