<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "新增品牌"
  }
}
</route>

<script lang="ts" setup>
import dayjs from 'dayjs'
import { storeToRefs } from 'pinia'
import ShowFormTip from '@/components/showFormTip/index.vue'
import { Color } from '@/enums/colorEnum'
import { useFormTip } from '@/hooks/useFormTip'
import { brandCreateApi } from '@/service/infoReportApi'
import { useUserStore } from '@/store/user'

const userStore = useUserStore()
const { userId } = storeToRefs(userStore)
const isShowDatePicker = ref(false)
const form = reactive({
  name: '',
  englishName: '',
  date: '',
  description: '',
})
const yearMonth = ref(0)
const { showTip } = useFormTip()

function saveDate() {
  // 用day.js时间戳转换为年月
  const year = dayjs(yearMonth.value).format('YYYY')
  const month = dayjs(yearMonth.value).format('MM')

  form.date = `${year}-${month}`
  isShowDatePicker.value = false
}

function handleSubmit() {
  brandCreateApi({
    userId: userId.value,
    brandCreatedDate: form.date,
    brandEnName: form.englishName,
    brandName: form.name,
    description: form.description,
  }).then(() => {
    uni.navigateBack()
  })
}

onLoad((option) => {
  if (option.action === 'create') {
    uni.setNavigationBarTitle({
      title: '新增品牌',
    })
  }
})
</script>

<template>
  <view class="p-2 space-y-2">
    <view class="rounded-md bg-white p-4 space-y-4">
      <view class="f-form-item">
        <view class="f-label" @click="showTip('品牌名称', '品牌名称长度不超过30个字符，内容不能包含“公司”、“厂”及特殊符号。')">
          <text class="o-form-require pr-1">
            品牌名称
          </text>
          <up-icon name="question-circle" :color="Color.gray" />
        </view>
        <view class="o-form-underline flex-1">
          <input v-model="form.name" placeholder="请输入品牌名称" placeholder-style="color: #9ca3af;">
        </view>
      </view>
      <view class="f-form-item">
        <view class="f-label">
          <text class="pr-1">
            英文名称
          </text>
        </view>
        <view class="o-form-underline flex-1">
          <input v-model="form.englishName" placeholder="请输入英文名称" placeholder-style="color: #9ca3af;">
        </view>
      </view>
      <view class="f-form-item">
        <view class="f-label">
          <text class="pr-1">
            创建年份
          </text>
        </view>
        <view class="o-form-underline flex flex-1 items-center" @click="isShowDatePicker = true">
          <view class="flex-1 pr-2">
            <text v-if="form.date !== ''">
              {{ form.date }}
            </text>
            <text v-else class="text-gray">
              请选择年月
            </text>
          </view>
          <up-icon name="arrow-right" :color="Color.gray" class="shrink-0" />
        </view>
      </view>
      <view class="f-label">
        品牌简介
      </view>
      <up-textarea v-model="form.description" placeholder="请输入品牌简介" count auto-height :maxlength="1000" :height="100" />
    </view>
    <view
      class="o-bg-primary o-shadow-blue flex flex-grow-1 items-center justify-center rd-2 p-3 text-white font-bold"
      @click="handleSubmit"
    >
      保存
    </view>
    <view class="o-pb" />
    <ShowFormTip />
    <up-datetime-picker
      v-model="yearMonth" :show="isShowDatePicker" mode="year-month"
      :close-on-click-overlay="true" @close="saveDate" @confirm="saveDate"
      @cancel="saveDate"
    />
  </view>
</template>

<style lang="scss" scoped>
.f-form-item {
  @apply flex items-center justify-between space-x-2 w-full shrink-0;
}

.f-label {
  @apply text-gray-500 flex items-center;
  min-width: 200rpx;
}
</style>
