// 图片上传最大大小 3M
export const UPLOAD_IMG_MAXSIZE = 1024 * 1024 * 3
export const BASE64IMG_PREFIX = 'data:image/jpeg;base64,'

export enum BarType {
  EAN13 = 'EAN13',
  ITF14 = 'ITF14',
}

/**
 * 通报用表单的条码类型
 */
export enum InfoReportBarType {
  UPC_A = 'UPC-A',
  EAN = 'EAN',
  ITF = 'ITF',
}

/**
 * 支付状态
 * @param new 新建：-1
 * @param notPay 未支付：0
 * @param paid 已支付：1
 * @param failed 支付失败：2
 */
export enum PayState {
  new = -1,
  notPay = 0,
  paid = 1,
  failed = 2,
}

/**
 * 协议id
 * @param privacy 隐私协议
 * @param platform 平台免责声明
 * @param design 设计免责声明
 * @param film 付费免责声明（条码制作）
 * @param infoReport 付费免责声明（产品编码信息通报）
 * @param miniShop 付费免责声明（二维码微站）
 * @param labelPrint 付费免责声明（标签印刷）
 * @param agencyService 付费免责声明（代办服务）
 */
export enum Article {
  privacy = 78, // 隐私协议
  platform = 77, // 平台免责声明
  design = 76, // 付款免责声明（设计）
  film = 75, // 付费免责声明（条码制作）
  infoReport = 74, // 付费免责声明（产品编码信息通报）
  miniShop = 79, // 付费免责声明（二维码微站）
  labelPrint = 80, // 付费免责声明（标签印刷）
  agencyService = 82, // 付费免责声明（业务代办）
}

/**
 * 优惠券类型
 * @param no 默认值0，前端用于默认为优惠券
 * @param cash 代金券1
 * @param discount 折扣券2
 */
export enum CouponType {
  no = 0, // 前端用于默认为优惠券
  cash = 1, // 代金券
  discount = 2, // 折扣券
}
export function couponTypeStr(couponType: CouponType) {
  switch (couponType) {
    case 1:
      return '代金券'
    case 2:
      return '折扣券'
    default:
      return '优惠券'
  }
}

/**
 * 服务类型
 * 定价模版类型：
 * 1：条码制作,
 * 2:信息上报,
 * 3:包装设计，
 * 4、标签印刷，
 * 5：店内码，
 * 6：续展；
 * 10：注册；
 * 12：变更；
 * 7：标签设计；
 * 8：打印机；
 * 9：进口商品报备；
 * 13：会员；
 * 14：快速通报
 */
export enum ServerType {
  makeFilm = 1, // 条码制作
  infoReport = 2, // 产品信息上报
  designServer = 3, // 设计
  labelPrint = 4, // 标签
  storeCode = 5, // 店内码
  renewalService = 6, // 续展
  // labelDesign = 7, // 标签设计
  // printer = 8, // 打印机
  importedGoods = 9, // 进口商品报备
  registerService = 10, // 注册
  miniShop = 11, // 二维码微站
  modifyService = 12, // 变更
  // member = 13, // 会员
  quickReport = 14, // 快速通报
}
const serverTypeMap: Map<ServerType, string> = new Map([
  [ServerType.makeFilm, '条码制作'],
  [ServerType.infoReport, '整批产品通报'],
  [ServerType.designServer, '包装设计'],
  [ServerType.labelPrint, '标签印刷'],
  [ServerType.storeCode, '条码制作'], // 店内码
  [ServerType.miniShop, '二维码微站'],
  [ServerType.renewalService, '条码续展'],
  [ServerType.importedGoods, '进口商品报备'],
  [ServerType.registerService, '条码会员注册'],
  [ServerType.modifyService, '条码会员变更'],
  [ServerType.quickReport, '逐个产品通报'],
])

export function getServerTypeStr(serverType: ServerType) {
  return serverTypeMap.get(serverType) || ''
}

/**
 * 商品状态
 * @param cache 缓存商品：0
 * @param unListed 未上架：1
 * @param listed 已上架：2
 */
export enum GoodsStatus {
  cache = 0,
  unListed = 1,
  listed = 2,
}

/**
 * 单一/多规格
 * @param single 单规格：1
 * @param multi 多规格：2
 */
export enum SpecType {
  single = 1,
  multi = 2,
}

export enum GoodsIdEnum {
  printer = 1,
  labelPrint = 3,
}

/**
 * 发票状态，1：已开票，0：未申请,2：已申请,-1:废票
 */
export enum InvoiceState {
  failed = -1, // 废票
  notApply = 0, // 未申请
  done = 1, // 已开票
  applying = 2, // 已申请
}

/**
 * 来源平台
 * @param wx 微信:商用条码生成器
 * @param douYin 抖音
 * @param other 其他
 * @param wx_tiaoMaBang 微信:条码帮
 */
export enum FromPlatform {
  wx = 1,
  douYin = 2,
  other = 3,
  wx_tiaoMaBang = 4,
}

export enum OrderStatus {
  failed = -1, // 办理失败
  waitingSubmit = 0, // 待提交资料
  hadSubmit = 1, // 资料已提交
  inProcess = 2, // 办理中
  done = 3, // 已完成
}

/**
 * 新增同步状态
 * -1 ：新增不成功，0：新增中，1：新增成功 2:新建
 */
export enum SyncAddState {
  failed = -1, // 新增不成功
  processing = 0, // 新增中
  success = 1, // 新增成功
  new = 2, // 新建
}

const syncAddStateMap: Map<SyncAddState, string> = new Map([
  [SyncAddState.failed, '通报失败'],
  [SyncAddState.processing, '通报中'],
  [SyncAddState.success, '已通报'],
  [SyncAddState.new, '未通报'],
])

export function getSyncAddStateStr(syncAddState: SyncAddState) {
  return syncAddStateMap.get(syncAddState) || ''
}

/**
 * 微信共享同步状态
 * -1 ：同步不成功，0：共享中，1：同步成功,2:新建
 */
export enum SyncWxState {
  failed = -1, // 同步不成功
  processing = 0, // 共享中
  success = 1, // 同步成功
  new = 2, // 新建
}
const syncWxStateMap: Map<SyncWxState, string> = new Map([
  [SyncWxState.failed, '微信共享失败'],
  [SyncWxState.processing, '微信共享中'],
  [SyncWxState.success, '微信已共享'],
  [SyncWxState.new, '微信未共享'],
])

export function getSyncWxStateStr(syncWxdState: SyncWxState) {
  return syncWxStateMap.get(syncWxdState) || ''
}

/**
 * 产品状态填写“在产或不在产”
 */
export enum GoodsType {
  inProduction = '在产',
  notInProduction = '不在产',
}
/**
 * 通报状态：0：通报中，1：通报完成，2：未通报
 */
export enum ReportStatus {
  reporting = 0, // 通报中
  done = 1, // 通报完成
  notReport = 2, // 未通报
}

/**
 * 货币类型
 */
export enum CurrencyType {
  RMB = '人民币',
  EUR = '欧元',
  USD = '美元',
}
