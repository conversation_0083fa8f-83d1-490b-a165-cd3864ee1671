/**
 * 校验手机号
 * @param rule
 * @param value
 * @param callback
 */
export function validatePhoneNumber(rule, value, callback) {
  const phoneRegex = /^1[3-9|]\d{9}$/
  return phoneRegex.test(value)
}

/**
 * 重设视频的宽度和高度
 * @param articleContent
 */
export function updateVideoDimensions(articleContent: string) {
  // 正则表达式用于查找 <video> 标签，并捕获 width 和 height 属性
  const videoRegex = /<video[^>]*width="[^"]*"[^>]*height="[^"]*"[^>]*>/gi

  // 替换函数，它会将匹配到的 <video> 标签中的 width 和 height 更新为目标值
  const replaceFn = (match: string) => {
    return match
      .replace(/width="[^"]*"/, 'width="100%"')
      .replace(/height="[^"]*"/, 'height="200px"')
  }

  // 使用正则表达式和替换函数来更新 articleContent 中的所有 <video> 标签
  return articleContent.replace(videoRegex, replaceFn)
}

// 使用正则表达式匹配 <body> 标签，并且替换为带有 style 属性的版本
export function addLineHeight(articleContent: string) {
  return articleContent.replace(
    /<body(?![^>]*style=)/i, // 查找 <body 并确保后面没有 style 属性
    '<body style="line-height:1.5em;"', // 替换为带有 style 属性的 <body
  )
}

/**
 * 校验邮箱
 * @param v
 */
export function validateMail(v: string) {
  const regex = /^[A-Z0-9\u4E00-\u9FA5]+@[\w-]+(?:\.[\w-]+)+$/i
  return regex.test(v)
}

/**
 * 将','替换成'，'
 * @param str
 */
export function replaceComma(str: string) {
  return str.replace(/,/g, '，')
}
