<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "条码注册申请"
  }
}
</route>

<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import RegisterStep from '@/components/agencyService/registerStep.vue'
import CsLongButton from '@/components/customer/csLongButton.vue'
import ServerRadioGroup from '@/components/serverRadioGroup.vue'
import ServerTitle from '@/components/serverTitle.vue'
import { ServerType } from '@/enums'
import { useCreateOrder } from '@/hooks/useCreateOrder'
import { useUserStore } from '@/store/user'
import { toAgencyPath } from '@/utils/'

const userStore = useUserStore()
const { existUncompletedRegister } = storeToRefs(userStore)

const { loading, isShowStep, serveData, selectServe, descriptionServiceType, handleSubmit }
  = useCreateOrder({
    orderConfirmUrl: '/pages/orderConfirm/default',
    paymentSuccessUrl: '/pages/paymentSuccess/agencyService',
  })
</script>

<template>
  <ServerTitle />
  <view class="mt-3 px-4">
    <view class="flex flex-col items-center justify-center rd-2 bg-white p-4">
      <view v-if="isShowStep" class="mb-6 pt-2">
        <RegisterStep />
      </view>
      <view class="o-color-aid flex items-center gap-3 text-sm" @click="isShowStep = !isShowStep">
        <up-icon name="question-circle" size="18" />
        <view>{{ isShowStep ? '隐藏' : '显示' }}办理说明</view>
        <up-icon v-if="isShowStep" name="arrow-up" size="16" />
        <up-icon v-else name="arrow-down" size="16" />
      </view>
    </view>
    <cs-long-button />
    <view class="mt-3 rd-2 bg-white p-4">
      <view class="mb-2 text-red-500 font-bold">
        关于条码容量：
      </view>
      <view class="o-p text-sm">
        新注册用户，条码容量为
        <text class="font-bold">
          1000
        </text>
        个，即最多只能拥有1000个条码编码。
      </view>
      <view class="o-p mt-1 text-sm">
        容量并非具体条码编号，具体每个条码可在本小程序另行制作。
      </view>
    </view>
    <view class="mt-3 rd-2 bg-white p-4">
      <view class="mb-3 pt-4 font-bold">
        服务方案：
      </view>
      <view id="targetElement" class="mt-4">
        <server-radio-group v-model="selectServe" :items="serveData" />
      </view>
    </view>
    <view class="p-10" />
    <view class="fixed bottom-0 left-0 z-10 box-border w-full p-4">
      <view class="flex gap-1">
        <view
          class="flex flex-grow-1 items-center justify-center rd-2 py-3 font-bold"
          :class="
            existUncompletedRegister
              ? 'bg-white text-primary shadow-lg'
              : 'o-bg-primary o-shadow-blue text-white'
          "
          @click="handleSubmit"
        >
          {{ existUncompletedRegister ? '继续下单' : '提交方案' }}
        </view>
        <view
          v-if="existUncompletedRegister"
          class="o-bg-primary o-shadow-blue flex flex-grow-1 items-baseline justify-center rd-2 py-3 text-white"
          @click="toAgencyPath(ServerType.registerService)"
        >
          <text class="text-sm">
            您有未完成的业务，
          </text>
          <text class="font-bold">
            前往办理
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped></style>
