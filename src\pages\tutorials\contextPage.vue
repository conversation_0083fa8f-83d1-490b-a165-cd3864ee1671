<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "文章内容"
  }
}
</route>

<script lang="ts" setup>
import type { ArticleLoadRes } from '@/service/tutorialsApi'
import { articleLoadApi } from '@/service/tutorialsApi'

const articleUrl = ref('')
const isUrl = ref(false)
const content = ref('')
const title = ref('')
const topImg = ref('')
const id = ref(0)
const DEFAULT_IMG = 'https://wx.gs1helper.com/images/p_index_chip_green_new.png'

const { loading, error, data, run } = useRequest<ArticleLoadRes>(() =>
  articleLoadApi({
    articleId: id.value,
  }),
)

onLoad((option) => {
  id.value = Number(option.id)
  run().then(() => {
    if (data.value.articleUrl) {
      articleUrl.value = data.value.articleUrl
      isUrl.value = true
    }
    else {
      // 给视频适配宽度和高度
      const r = updateVideoDimensions(data.value.articleConent)
      // 添加默认行高
      content.value = addLineHeight(r)
      title.value = data.value.articleTitle
      topImg.value = `url(${data.value.imageUrl || DEFAULT_IMG})`
    }
  })
})

// 使用正则表达式匹配 <body> 标签，并且替换为带有 style 属性的版本
function addLineHeight(articleContent: string) {
  return articleContent.replace(
    /<body(?![^>]*style=)/i, // 查找 <body 并确保后面没有 style 属性
    '<body style="line-height:1.5em;"', // 替换为带有 style 属性的 <body
  )
}

function updateVideoDimensions(articleContent: string) {
  // 正则表达式用于查找 <video> 标签，并捕获 width 和 height 属性
  const videoRegex = /<video[^>]*width="[^"]*"[^>]*height="[^"]*"[^>]*>/gi

  // 替换函数，它会将匹配到的 <video> 标签中的 width 和 height 更新为目标值
  const replaceFn = (match: string) => {
    return match
      .replace(/width="[^"]*"/, 'width="100%"')
      .replace(/height="[^"]*"/, 'height="200px"')
  }

  // 使用正则表达式和替换函数来更新 articleContent 中的所有 <video> 标签
  return articleContent.replace(videoRegex, replaceFn)
}
</script>

<template>
  <web-view v-if="isUrl" :src="articleUrl" />
  <view v-else class="f-page bg-white">
    <view v-if="loading" class="p-6">
      <up-skeleton rows="3" title loading :animate="true" />
    </view>
    <view v-else>
      <view class="f-top-img w-full" :style="{ backgroundImage: topImg }" />
      <view class="px-6">
        <view
          class="px-6 pt-6 text-center text-xl font-bold"
          style="word-break: break-all; line-break: anywhere"
        >
          {{ title }}
        </view>
        <up-divider />
        <up-parse :content="content" />
        <view class="f-divider mb-4 ml-auto mr-auto mt-16">
          <up-divider />
        </view>
        <view class="o-logo w-full" />
        <view class="p-6" />
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.f-page {
  min-height: 100vh;
}

.f-top-img {
  height: 280rpx;
  background-repeat: no-repeat;
  background-position-x: center;
  background-position-y: 52%;
  background-size: cover;
}

.o-logo {
  height: 80rpx;
}

.f-divider {
  width: 60%;
}
</style>
