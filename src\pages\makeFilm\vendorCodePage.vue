<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page -->
<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "查询厂商识别代码"
  }
}
</route>

<script lang="ts" setup>
import type { GetCompanyVendorCodeResData } from '@/service/barcodePageApi'
import debounce from 'debounce'
import { storeToRefs } from 'pinia'
import { jumpToWeChatCustomerService } from '@/hooks/useCustomerService'
import { getCompanyVendorCodeApi } from '@/service/barcodePageApi'
import { vendorCodeStore } from '@/store/vendorCodeStore'

const useVendorCodeStore = vendorCodeStore()
const { companyName, vendorCode } = storeToRefs(useVendorCodeStore)

const companyNameInput = ref('')
const showEmptyIcon = ref(false)
const list = ref<any[]>([])

// 将字符串所有中文"（"和"）"转成英文"("和")"
function toEnglish(str: string) {
  return str.replace(/（/g, '(').replace(/）/g, ')')
}

function getData() {
  showEmptyIcon.value = false
  list.value = []
  uni.showLoading({
    title: '加载中',
  })
  getCompanyVendorCodeApi({
    companyName: toEnglish(companyNameInput.value),
  })
    .then((res) => {
      if (res.data.length > 0) {
        list.value = res.data
      }
      else {
        debounce(() => {
          showEmptyIcon.value = true
        }, 2000)
      }
    })
    .catch(() => {
      showEmptyIcon.value = true
    })
    .finally(() => {
      uni.hideLoading()
    })
}

function handleSearch() {
  // companyNameInput至少6个字
  if (companyNameInput.value.length >= 2) {
    getData()
  }
  else {
    uni.showToast({
      title: '请输入至少2个字',
      icon: 'none',
    })
  }
}

function handleSelect(data: GetCompanyVendorCodeResData) {
  companyName.value = data.companyName
  vendorCode.value = data.vendorCode
  uni.navigateBack()
}

const handleInput = debounce(() => {
  handleSearch()
}, 600)
</script>

<template>
  <view class="sticky left-0 top-0 z-1 w-full">
    <view class="bg-white px-4 py-2">
      <view class="o-bg-no flex items-center gap-2 py-1 pl-4 pr-3" style="border-radius: 2rem">
        <up-input
          v-model="companyNameInput"
          :maxlength="50"
          border="none"
          class="o-bg-transparent grow"
          clearable
          placeholder="请输入公司名称"
          @change="handleInput"
        />
        <up-icon name="search" size="20" @click="handleSearch" />
      </view>
    </view>
  </view>
  <up-alert
    v-if="list.length > 0"
    type="warning"
    description="点击即可回填厂商识别码"
    :show-icon="true"
    closable
  />
  <view class="flex flex-col pb-6 pt-1">
    <view
      v-for="item in list"
      :key="item.vendorCode"
      class="f-item flex justify-between px-3 py-4 text-sm"
      @click="handleSelect(item)"
    >
      <view>{{ item.companyName }}</view>
      <view class="color-gray">
        {{ item.vendorCode }}
      </view>
    </view>
    <view v-if="companyNameInput" class="o-color-aid mt-20 center flex-col px-6 text-sm">
      <text class="text-center">
        未查到数据？公司名是否正确？
      </text>
      <text class="text-center">
        数据存在滞后，如已知厂商识别代码，
      </text>
      <text class="text-center">
        可直接返回输入
      </text>
      <text class="mt-6 text-center">
        紧急可先联系客服获取
      </text>
      <button
        class="f-customer-service mt-2 px-3 pb-2 pt-3 text-sm"
        @click="jumpToWeChatCustomerService"
      >
        <image
          class="o-cs-img mr-1"
          src="https://wx.gs1helper.com/images/p_index_customer_service.png"
        />
        联系客服
      </button>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.f-item:nth-child(even) {
  background-color: #fff;
}

.f-customer-service {
  width: 40%;
  color: rgba(0, 0, 0, 0.85);
  background-color: #fff;
}
</style>
