import type { OrderOtherInfoListResData } from '@/service/agencyServiceApi'
import { defineStore } from 'pinia'

export const submitServiceStore = defineStore('submitServiceStore', () => {
  const orderInfoData = ref<OrderOtherInfoListResData>({
    barCodeCardNum: '',
    barCodeCardPassword: '',
    companyLicenseChangeImage: '',
    companyLicenseChangeUrl: '',
    companyLicenseImage: '',
    companyLicenseUrl: '',
    contact: '',
    contactPhone: '',
    email: '',
    isHasChange: true,
    orderCode: '',
    orderContent: '',
    orderId: 0,
    otherStatus: 0,
    otherStatusStr: '',
    payCetificate: '',
    payCetificateImage: '',
    payDate: '',
    reason: '',
    registrationForm: '',
    registrationFormImage: '',
    legalPhone: '',
  })

  return { orderInfoData }
})
