<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "支付成功"
  }
}
</route>

<script lang="ts" setup>
import type { ServerType } from '@/enums'
import { storeToRefs } from 'pinia'
import { DESCRIPTION_STR } from '@/components/descriptionStr'
import CouponCard from '@/components/Price/CouponCard.vue'
import PriceBox from '@/components/Price/PriceBox.vue'
import { getCouponApi } from '@/service/orderApi'
import { orderStore } from '@/store/orderStore'

const useOrderStore = orderStore()
const { orderCode, tempName, actuallyPrice } = storeToRefs(useOrderStore)

const couponName = ref('')
const couponPrice = ref(0)
const couponType = ref(2)
const description = ref('')
const discount = ref(0)
const serverType = ref<ServerType>()
const expirySeconds = ref(0)
const vendorCode = ref('')
const showCoupon = ref(false)

getCouponApi({ orderCode: orderCode.value }).then((res) => {
  const d = res.data
  if (d?.couponId) {
    couponName.value = d.couponName
    couponPrice.value = d.couponPrice
    couponType.value = d.couponType
    description.value = d.description
    discount.value = d.discount
    serverType.value = d.serverType
    expirySeconds.value = d.expirySeconds
    vendorCode.value = d.vendorCode
    showCoupon.value = true
  }
})
</script>

<template>
  <view class="px-4 pb-10 pt-4">
    <view class="pl-3 text-xl text-primary font-bold">
      订单支付成功！
    </view>
    <view v-if="showCoupon" class="pl-3 text-sm text-primary">
      恭喜获得一张折扣券
    </view>
    <view class="mt-2 rd-2 bg-white">
      <view class="p-4">
        <view class="o-color-aid text-xs">
          订单编号：{{ orderCode }}
        </view>
        <view class="o-color-aid mt-2 text-xs">
          服务类型：
        </view>
        <view class="mb-2 flex items-center">
          {{ tempName }}
        </view>
        <view class="flex items-baseline justify-end">
          <view class="text-sm">
            实付：
          </view>
          <price-box :price="actuallyPrice" :size="48" />
        </view>
        <view class="o-line mb-4 mt-4" />
        <view class="mb-2 text-sm font-bold">
          印前合规检查步骤：
        </view>
        <view
          v-for="(item, index) in DESCRIPTION_STR.designServer.makeStep"
          :key="index"
          class="flex text-xs"
        >
          <view class="min-w-5 shrink-0 text-primary font-bold">
            {{ index + 1 }}.
          </view>
          <view>{{ item }}</view>
        </view>
        <view class="mb-2 mt-6 text-sm font-bold">
          设计服务步骤：
        </view>
        <view
          v-for="(item, index) in DESCRIPTION_STR.designServer.designStep"
          :key="index"
          class="flex text-xs"
        >
          <view class="min-w-5 shrink-0 text-primary font-bold">
            {{ index + 1 }}.
          </view>
          <view>{{ item }}</view>
        </view>
      </view>
      <template v-if="showCoupon">
        <view class="f-coupon-dot mb-4 w-full" />
        <view class="p-4">
          <coupon-card
            :data="{
              couponName,
              couponPrice,
              couponType,
              description,
              serverType,
              discount,
              expirySeconds,
              vendorCode,
            }"
          />
        </view>
      </template>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.f-coupon-dot {
  height: 50rpx;
  background-image: url('https://wx.gs1helper.com/images/p_coupon_o_o_o.png');
  background-repeat: no-repeat;
  background-position-y: 50%;
  background-size: contain;
}
</style>
