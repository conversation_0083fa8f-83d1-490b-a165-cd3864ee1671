# Build
1. 先运行`selectPlatform:wx`或`selectPlatform:wx_tiaoMaBang`，切换【微信：商用条码生成器】或【微信：条码帮】。
2. 再`dev`或`build`

# ⚠️注意

- 只有[pages](src/pages)的页面才能放本文件夹，组件别放进去，因为unibest框架会自动给定义成页面，会出错。应放components[components](src/components)文件夹

# 主题

- [可改值](node_modules/wot-design-uni/components/common/abstracts/variable.scss) 写到 [这里](src/style/index.scss)

### 字号大小

| 层级                     | CSS       | px   | rpx | 字号变量           | 字体颜色变量    |
| ------------------------ | --------- | ---- | --- | ------------------ | --------------- |
| 大型标题                 | text-2xl  | 24px | 44  | --wot-fs-big       | $-color-content |
| 重要数据                 | text-xl   | 19px | 40  | --wot-fs-important | $-color-content |
| 标题字号/重要正文字号    | text-lg   | 16px | 36  | --wot-fs-title     | $-color-content |
| 普通正文                 | text-base | 14px | 32  | --wot-fs-content   | $-color-content |
| 次要信息，注释/补充/正文 | text-sm   | 12px | 28  | --wot-fs-secondary | $-color-aid     |

| 辅助文字，弱化信息，引导性/不可点文字 | text-xs | 10px | 24 | --wot-fs-aid | $-color-aid |

### color

- green: emerald-500
- yellow: amber

## OSS

- oss白名单备份：
````
*.console.aliyun.com
*.gs1helper.com
*servicewechat.com
http://192.168.*.*:*
https://192.168.*.*:*
````


## 提交

- feat: 表示新增功能或特性（feature）。
  - 示例：feat: 添加用户登录功能

- fix: 表示修复 bug 或问题。
  - 示例：fix: 修复登录页面无法提交的问题

- perf: 表示性能优化（performance）。
  - 示例：perf: 优化数据库查询性能

- style: 表示代码样式或格式的更改，不影响代码逻辑（如空格、缩进、分号等）。
  - 示例：style: 格式化代码缩进

- docs: 表示文档更新（documentation）。
  - 示例：docs: 更新 README 文件

- test: 表示测试代码的添加或修改。
  - 示例：test: 添加用户登录功能的单元测试

- refactor: 表示代码重构，既不修复 bug 也不添加新功能。
  - 示例：refactor: 重构用户模块的代码结构

- build: 表示构建系统或外部依赖的更改（如 Webpack、npm 等）。
  - 示例：build: 更新 Webpack 配置

- ci: 表示持续集成（Continuous Integration）配置或脚本的更改（如 GitHub Actions、Travis CI 等）。
  - 示例：ci: 添加 GitHub Actions 工作流

- chore: 表示日常维护任务或杂项更改（如更新配置文件、清理代码等）。
  - 示例：chore: 更新 .gitignore 文件

- revert: 表示回滚之前的提交。
  - 示例：revert: 回滚提交 abc123

- wip: 表示正在进行中的工作（Work In Progress），通常用于未完成的提交。
  - 示例：wip: 用户注册功能开发中

- workflow: 表示工作流程的更改（如 Git 分支策略、代码审查流程等）。
  - 示例：workflow: 更新代码合并流程

- types: 表示类型定义的更改（通常用于 TypeScript 项目）。
  - 示例：types: 更新用户接口类型定义

- release: 表示发布新版本。
  - 示例：release: 发布 v1.0.0


# 开发注意
- 条件编译scss时，不能写在大括号中。
````scss
/*  #ifndef MP-TOUTIAO  */
.o-vf-up-radio-group {
  .u-radio {
    @apply w-full;

    margin-top: 0 !important;
    margin-bottom: 0 !important;

    &:nth-of-type(even) {
      @apply py-1;
      background-color: var(--o-body-bg-color);
    }
  }
}
/*  #endif  */
````
- uview-plus 的组合组件，例如<up-form/><up-radio-group/>只能写在page页面中，如何写在组件中，会报错。
