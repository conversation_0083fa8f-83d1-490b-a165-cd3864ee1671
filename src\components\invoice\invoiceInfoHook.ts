import { validateMail, validatePhoneNumber } from '@/utils/tool'

export function useInvoiceInfoHook() {
  const model = reactive<{
    contact: string
    contactPhone: string
    companyName: string
    creditCode: string
    email: string
    bank: string
    bankCode: string
    phone: string
    address: string
  }>({
    contact: '',
    contactPhone: '',
    companyName: '',
    creditCode: '',
    email: '',
    bank: '',
    bankCode: '',
    phone: '',
    address: '',
  })

  const form = ref()

  const validateIdentificationNumber = (rule, value, callback) => {
    // const regex = /^[0-9a-zA-Z]{15}|^[0-9a-zA-Z]{18}|^[0-9a-zA-Z]{20}$/
    const regex = /^[A-Z0-9]{15}$|^[A-Z0-9]{17}$|^[A-Z0-9]{18}$|^[A-Z0-9]{20}$/
    return regex.test(value)
  }

  const validateBankCode = (rule, value, callback) => {
    if (value !== '') {
      const regex = /^[1-9]\d{9,29}$/
      return regex.test(value)
    }
    else {
      return true
    }
  }

  return {
    model,
    form,
    validatePhoneNumber,
    validateMail,
    validateIdentificationNumber,
    validateBankCode,
  }
}
