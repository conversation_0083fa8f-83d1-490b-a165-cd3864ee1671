import type { DownloadOrderInvoiceParamsOrderList } from '@/service/orderApi'
import { defineStore } from 'pinia'
import { ref } from 'vue'

export const invoiceOrderStore = defineStore('invoiceOrderStore', () => {
  const orderObjList = ref<DownloadOrderInvoiceParamsOrderList[]>([])
  // orderSuccess用于订单成功后刷新订单列表
  const orderSuccess = ref(false)

  return {
    orderObjList,
    orderSuccess,
  }
})
