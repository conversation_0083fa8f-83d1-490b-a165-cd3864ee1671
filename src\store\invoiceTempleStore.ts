import { defineStore } from 'pinia'
import { ref } from 'vue'

export const invoiceTempleStore = defineStore('invoiceTempleStore', () => {
  const invoiceTempId = ref(0)
  const invoiceData = ref<{
    contact: string
    contactPhone: string
    companyName: string
    creditCode: string
    email: string
    bank: string
    bankCode: string
    phone: string
    address: string
  }>({
    address: '',
    bank: '',
    bankCode: '',
    companyName: '',
    contact: '',
    contactPhone: '',
    creditCode: '',
    email: '',
    phone: '',
  })

  return { invoiceData, invoiceTempId }
})
