import { defineUniPages } from '@uni-helper/vite-plugin-uni-pages'
import { tabBar } from './src/tabbar/config'

export default defineUniPages({
  globalStyle: {
    navigationStyle: 'default',
    navigationBarTitleText: 'barcode_center_wx',
    navigationBarBackgroundColor: '#f8f8f8',
    navigationBarTextStyle: 'black',
    backgroundColor: '#FFFFFF',
  },
  easycom: {
    autoscan: true,
    custom: {
      '^fg-(.*)': '@/components/fg-$1/fg-$1.vue',
      // 下面的是 uview-plus 的配置
      '^u--(.*)': 'uview-plus/components/u-$1/u-$1.vue',
      '^up-(.*)': 'uview-plus/components/u-$1/u-$1.vue',
      '^u-([^-].*)': 'uview-plus/components/u-$1/u-$1.vue',
      // 下面是z-paging 的配置
      '^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)':
        'z-paging/components/z-paging$1/z-paging$1.vue',
    },
  },
  // tabbar 的配置统一在 “./src/layouts/fg-tabbar/tabbarList.ts” 文件中
  /* // 这个是微信的地址输入插件，不用
    plugins: {
    'address-form': {
      version: '1.0.2', // 插件版本
      provider: 'wx57d7ae552cbb3084',
      export: 'config.js', // 步骤3.2的配置文件
    },
  }, */
  tabBar: tabBar as any,
  condition: {
    // 模式配置，仅开发期间生效
    current: 1, // 当前激活的模式(list 的索引项)
    list: [
      {
        name: 'indexWithQuery',
        path: 'pages/index/index',
        query: 'scene=emailop%3DXGxvB',
      },
    ],
  },
})
