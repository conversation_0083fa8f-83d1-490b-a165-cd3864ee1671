{"name": "barcode_mini_app", "type": "commonjs", "version": "0.0.1", "description": "", "engines": {"node": ">=18", "pnpm": ">=7.30"}, "scripts": {"preinstall": "npx only-allow pnpm", "uvm": "npx @dcloudio/uvm@latest", "uvm-rm": "node ./scripts/postupgrade.js", "postuvm": "echo upgrade uni-app success!", "dev:app": "uni -p app", "dev:app-android": "uni -p app-android", "dev:app-ios": "uni -p app-ios", "dev:custom": "uni -p", "dev": "uni", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp": "uni -p mp-weixin", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-jd": "uni -p mp-jd", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:mp-xhs": "uni -p mp-xhs", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:app": "uni build -p app", "build:app-android": "uni build -p app-android", "build:app-ios": "uni build -p app-ios", "build:custom": "uni build -p", "build:h5": "uni build", "build": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp": "uni build -p mp-weixin", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-jd": "uni build -p mp-jd", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:mp-xhs": "uni build -p mp-xhs", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "prepare": "git init && husky install ", "type-check": "vue-tsc --noEmit", "release": "standard-version", "cz": "czg"}, "lint-staged": {"**/*.{html,vue,ts,cjs,json,md}": ["prettier --write"], "**/*.{vue,js,ts,jsx,tsx}": ["eslint --cache --fix"], "**/*.{vue,css,scss,html}": ["stylelint --fix --allow-known-properties"]}, "resolutions": {"bin-wrapper": "npm:bin-wrapper-china"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4060520250512001", "@dcloudio/uni-app-harmony": "3.0.0-4060520250512001", "@dcloudio/uni-app-plus": "3.0.0-4060520250512001", "@dcloudio/uni-components": "3.0.0-4060520250512001", "@dcloudio/uni-h5": "3.0.0-4060520250512001", "@dcloudio/uni-mp-alipay": "3.0.0-4060520250512001", "@dcloudio/uni-mp-baidu": "3.0.0-4060520250512001", "@dcloudio/uni-mp-harmony": "3.0.0-4060520250512001", "@dcloudio/uni-mp-jd": "3.0.0-4060520250512001", "@dcloudio/uni-mp-kuaishou": "3.0.0-4060520250512001", "@dcloudio/uni-mp-lark": "3.0.0-4060520250512001", "@dcloudio/uni-mp-qq": "3.0.0-4060520250512001", "@dcloudio/uni-mp-toutiao": "3.0.0-4060520250512001", "@dcloudio/uni-mp-weixin": "3.0.0-4060520250512001", "@dcloudio/uni-mp-xhs": "3.0.0-4060520250512001", "@dcloudio/uni-quickapp-webview": "3.0.0-4060520250512001", "clipboard": "^2.0.11", "dayjs": "1.11.10", "debounce": "^2.2.0", "pinia": "2.0.36", "pinia-plugin-persistedstate": "3.2.1", "qs": "6.5.3", "uview-plus": "^3.4.31", "vue": "3.4.21", "vue-i18n": "^9.14.4", "z-paging": "^2.8.6"}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@dcloudio/types": "^3.4.15", "@dcloudio/uni-automator": "3.0.0-4060520250512001", "@dcloudio/uni-cli-shared": "3.0.0-4060520250512001", "@dcloudio/uni-stacktracey": "3.0.0-4060520250512001", "@dcloudio/vite-plugin-uni": "3.0.0-4060520250512001", "@douyin-microapp/typings": "^1.3.1", "@esbuild/darwin-arm64": "0.20.2", "@esbuild/darwin-x64": "0.20.2", "@iconify-json/carbon": "^1.2.8", "@rollup/rollup-darwin-x64": "^4.40.2", "@types/node": "^20.17.47", "@types/wechat-miniprogram": "^3.4.8", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@uni-helper/uni-types": "1.0.0-alpha.6", "@uni-helper/vite-plugin-uni-layouts": "^0.1.10", "@uni-helper/vite-plugin-uni-manifest": "^0.2.8", "@uni-helper/vite-plugin-uni-pages": "0.2.28", "@uni-helper/vite-plugin-uni-platform": "^0.0.4", "@vue/runtime-core": "^3.5.14", "@vue/tsconfig": "^0.1.3", "autoprefixer": "^10.4.21", "commitlint": "^19.8.1", "czg": "^1.11.1", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-config-standard": "^17.1.0", "eslint-import-resolver-typescript": "^3.10.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-vue": "^9.33.0", "husky": "^8.0.3", "lint-staged": "^15.5.2", "postcss": "^8.5.3", "postcss-html": "^1.8.0", "postcss-scss": "^4.0.9", "rollup-plugin-visualizer": "^5.14.0", "sass": "1.63.2", "sass-loader": "10.4.1", "stylelint": "^16.19.1", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^4.6.0", "stylelint-config-recommended": "^14.0.1", "stylelint-config-recommended-scss": "^14.1.0", "stylelint-config-recommended-vue": "^1.6.0", "stylelint-prettier": "^5.0.3", "terser": "^5.39.2", "typescript": "^5.8.3", "unocss": "66.1.0-beta.8", "unocss-applet": "^0.9.0", "unplugin-auto-import": "^19.2.0", "vite": "5.2.8", "vite-plugin-restart": "^0.4.2", "vitepress": "^1.6.3", "vue-tsc": "^2.2.10"}, "pnpm": {"onlyBuiltDependencies": ["core-js", "esbuild", "unrs-resolver", "vue-demi"]}}