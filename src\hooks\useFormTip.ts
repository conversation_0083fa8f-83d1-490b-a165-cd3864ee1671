import { reactive, ref } from 'vue'

// Global state for form tip
const isShowTip = ref<boolean>(false)
const tipContent = reactive({
  title: '',
  content: '',
})

export function useFormTip() {
  function showTip(title: string, msg: string) {
    tipContent.title = title
    tipContent.content = msg
    isShowTip.value = true
  }

  function hideTip() {
    isShowTip.value = false
  }

  return {
    isShowTip,
    tipContent,
    showTip,
    hideTip,
  }
}
