import uviewPlus from 'uview-plus'
import { createSSRApp } from 'vue'
import App from './App.vue'
import { requestInterceptor } from './http/interceptor'
// import { routeInterceptor } from './router/interceptor'
import store from './store'
import '@/style/index.scss'
import 'virtual:uno.css'
import '@/style/myStyle.scss'

export function createApp() {
  const app = createSSRApp(App)
  app.use(store)
  // app.use(routeInterceptor) // 不需要路由守卫
  app.use(requestInterceptor)
  app.use(uviewPlus)
  /*   app.use(uviewPlus, () => {
    return {
      options: {
        config: {
          // 默认字体图标自托管资源地址
          iconUrl: 'https://at.alicdn.com/t/font_2225171_8kdcwk4po24.ttf',
        },
      },
    }
  }) */
  return {
    app,
  }
}
