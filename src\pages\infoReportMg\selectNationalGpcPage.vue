<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "选择国补产品分类"
  }
}
</route>

<script lang="ts" setup>
import type { GpcNationalChildrenData, ReportGpcPageRes } from '@/service/infoReportApi'
import type { GpcNationalChildrenDataWithLevel } from '@/store/nationalGpcStore'
import debounce from 'debounce'
import { storeToRefs } from 'pinia'
import { Color } from '@/enums/colorEnum'
import { gpcNationalListApi } from '@/service/infoReportApi'
import { infoReportEditStore } from '@/store/infoReportEditStore'
import { nationalGpcStore } from '@/store/nationalGpcStore'

const useInfoReportEditStore = infoReportEditStore()
const { activeGpcItem } = storeToRefs(useInfoReportEditStore)
const useNationalGpcStore = nationalGpcStore()
const { nationalGpcData, levelNationalGpcData } = storeToRefs(useNationalGpcStore)
const thisPageNationalGpcData = ref<GpcNationalChildrenDataWithLevel[]>([])
const keyword = ref('')
const loading = ref(false)
const pageIndex = ref(1)
const level = ref(0)
const hasMore = ref(true)

const handleInput = debounce(() => {
  handleSearch()
}, 800)

function handleSearch() {
  if (!keyword.value.trim()) {
    // 如果搜索关键词为空，恢复原始数据
    if (level.value > 0) {
      thisPageNationalGpcData.value = JSON.parse(JSON.stringify(nationalGpcData.value))
    }
    else {
      thisPageNationalGpcData.value = JSON.parse(JSON.stringify(useNationalGpcStore.getLevelNationalGpcData()))
    }
    return
  }

  // 递归搜索函数，将所有匹配的项平铺到一个数组中
  function flatSearchInData(data: GpcNationalChildrenDataWithLevel[], searchTerm: string): GpcNationalChildrenDataWithLevel[] {
    const results: GpcNationalChildrenDataWithLevel[] = []

    for (const item of data) {
      // 检查当前项是否匹配
      const isMatch = item.name.toLowerCase().includes(searchTerm.toLowerCase())

      if (isMatch) {
        // 如果匹配，将此项添加到结果中（保持原有的children结构）
        results.push({
          ...item,
        })
      }

      // 递归搜索子项
      if (item.children && item.children.length > 0) {
        const childResults = flatSearchInData(item.children, searchTerm)
        results.push(...childResults)
      }
    }

    return results
  }

  // 确定搜索的数据源
  const sourceData = level.value > 0
    ? nationalGpcData.value
    : useNationalGpcStore.getLevelNationalGpcData()

  // 执行搜索，将所有匹配的项平铺到第一层
  thisPageNationalGpcData.value = flatSearchInData(sourceData, keyword.value.trim())
}

function handleSelect(dataItem: GpcNationalChildrenDataWithLevel) {
  if (dataItem.level === 4) {
    // 回退回infoReportDetailFromPage页面
    activeGpcItem.value = {
      code: dataItem.gpcCode,
      gpcTypeName: dataItem.name,
    }
    // 获取当前页面栈
    const pages = getCurrentPages()
    // 找到infoReportDetailFromPage页面的索引
    let targetIndex = -1
    for (let i = pages.length - 1; i >= 0; i--) {
      if (pages[i].route.includes('infoReportDetailFromPage')) {
        targetIndex = i
        break
      }
    }

    if (targetIndex !== -1) {
      // 计算需要回退的层数
      const delta = pages.length - 1 - targetIndex
      uni.navigateBack({ delta })
    }
    else {
      // 如果找不到目标页面，直接回退一层
      uni.navigateBack()
    }
  }
  else {
    console.log('🚀 ~ handleSelect ~ dataItem:', dataItem)
    levelNationalGpcData.value = JSON.parse(JSON.stringify(dataItem.children))
    uni.navigateTo({
      url: '/pages/infoReportMg/selectNationalGpcPage',
    })
  }
}

function refresh() {
  if (loading.value || !hasMore.value)
    return
  if (levelNationalGpcData.value.length > 0 && levelNationalGpcData.value[0].level === 1) {
    getGpcData()
  }
}

// 递归添加level字段，将原始数据转换为带level的数据
function addLevelToData(data: GpcNationalChildrenData[], level: number = 1): GpcNationalChildrenDataWithLevel[] {
  return data.map(item => ({
    gpcCode: item.gpcCode,
    name: item.name,
    level,
    children: addLevelToData(item.children, level + 1),
  }))
}

function getGpcData() {
  return new Promise((resolve, reject) => {
    loading.value = true
    gpcNationalListApi().then((res) => {
      nationalGpcData.value = addLevelToData(res.data)
      resolve(true)
    }).catch((res) => {
      reject(res)
    }).finally(() => {
      loading.value = false
    })
  })
}

onLoad((option) => {
  if (option.level) {
    level.value = Number(option.level)
    nationalGpcData.value = []
    getGpcData().then(() => {
      thisPageNationalGpcData.value = JSON.parse(JSON.stringify(nationalGpcData.value))
    })
  }
  else {
    thisPageNationalGpcData.value = JSON.parse(JSON.stringify(useNationalGpcStore.getLevelNationalGpcData()))
  }
})
</script>

<template>
  <view>
    <view
      class="f-search-bar fixed left-0 top-0 z-1 box-border w-full flex items-center bg-white px-3 py-2 shadow-blue"
    >
      <view class="o-bg-no flex flex-1 items-center gap-2 py-1 pl-4 pr-3" style="border-radius: 2rem">
        <up-icon :color="Color.gray" name="search" size="20" />
        <up-input
          v-model="keyword" :maxlength="30" border="none" class="o-bg-transparent grow" clearable
          placeholder="请输入产品分类名称搜索" @change="handleInput"
        />
      </view>
    </view>
    <up-list class="box-border px-6 pt-14" :enable-back-to-top="true" @scrolltolower="refresh">
      <up-list-item v-for="(item) in thisPageNationalGpcData" :key="item.gpcCode">
        <view class="f-item flex flex-wrap items-baseline py-2 space-x-1" @click="handleSelect(item)">
          <view v-if="item.level !== 1" class="flex items-baseline">
            <text class="text-xs text-gray-400">
              ...
            </text>
            <up-icon name="arrow-right" color="#9ca3af" size="12" />
          </view>
          <view class="rounded bg-white px-3 py-1">
            {{ item.name }}
          </view>
          <view v-if="item.level !== 4" class="flex items-baseline">
            <up-icon name="arrow-right" color="#9ca3af" size="12" />
            <text class="text-xs text-gray-400">
              ...
            </text>
          </view>
        </view>
      </up-list-item>

      <!-- 加载状态提示 -->
      <view v-if="loading" class="flex justify-center py-4">
        <up-loading-icon mode="semicircle" />
        <text class="ml-2 text-gray-500">加载中...</text>
      </view>

      <!-- 没有更多数据提示 -->
      <view v-else-if="!hasMore" class="flex justify-center py-8">
        <text class="text-gray-400">没有更多数据了</text>
      </view>
    </up-list>
  </view>
</template>

<style lang="scss" scoped>
.f-item {
  border-bottom: 1px solid #eee;
}
</style>
