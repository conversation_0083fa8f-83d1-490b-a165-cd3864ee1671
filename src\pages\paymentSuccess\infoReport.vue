<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "支付成功"
  }
}
</route>

<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import { getTemplateIds } from '@/components/descriptionStr'
import QuickReportStep from '@/components/infoReport/quickReportStep.vue'
import ReportStep from '@/components/infoReport/reportStep.vue'
import NeedPhoneForm from '@/components/needPhoneForm.vue'
import CouponCard from '@/components/Price/CouponCard.vue'
import PriceBox from '@/components/Price/PriceBox.vue'
import { ServerType } from '@/enums'
import { getCouponApi } from '@/service/orderApi'
import { orderStore } from '@/store/orderStore'

const useOrderStore = orderStore()
const { orderCode, serverType, tempName, actuallyPrice } = storeToRefs(useOrderStore)

const couponName = ref('')
const couponPrice = ref(0)
const couponType = ref(2)
const description = ref('')
const discount = ref(0)
const couponServerType = ref<ServerType>()
const expirySeconds = ref(0)
const vendorCode = ref('')
const showCoupon = ref(false)

onLoad(() => {
  getCouponApi({ orderCode: orderCode.value }).then((res) => {
    const d = res.data
    if (d?.couponId) {
      couponName.value = d.couponName
      couponPrice.value = d.couponPrice
      couponType.value = d.couponType
      description.value = d.description
      discount.value = d.discount
      couponServerType.value = d.serverType
      expirySeconds.value = d.expirySeconds
      vendorCode.value = d.vendorCode
      showCoupon.value = true
    }
  })
  uni.showModal({
    title: '订阅消息',
    content: '为了通知您办理结果，请允许订阅消息',
    confirmText: '确定',
    cancelText: '取消',
    confirmColor: '#165dff',
    complete() {
      wx.requestSubscribeMessage({
        tmplIds: getTemplateIds(),
        success(res) {
          console.log('🚀 ~ success ~ res:', res)
        },
        fail(err) {
          console.log('🚀 ~ fail ~ err:', err)
          uni.showToast({
            title: '订阅消息失败',
            icon: 'none',
          })
        },
      })
    },
  })
})
</script>

<template>
  <view class="px-4 pb-10 pt-4">
    <view class="pl-3 text-xl text-primary font-bold">
      订单支付成功！
    </view>
    <view v-if="showCoupon" class="pl-3 text-sm text-primary">
      恭喜获得一张折扣券
    </view>
    <view class="mt-2 rd-2 bg-white p-4">
      <view class="o-color-aid text-xs">
        订单编号：{{ orderCode }}
      </view>
      <view class="o-color-aid mt-2 text-xs">
        产品编码信息通报
      </view>
      <view class="mb-2 flex items-center">
        {{ tempName }}
      </view>
      <view class="flex items-baseline justify-end">
        <view class="text-sm">
          实付：
        </view>
        <price-box :price="actuallyPrice" :size="48" />
      </view>
    </view>
    <NeedPhoneForm />
    <view class="mt-3 rd-2 bg-white">
      <view class="p-4">
        <ReportStep v-if="serverType === ServerType.infoReport" :is-success="true" />
        <QuickReportStep v-else />
      </view>
      <template v-if="showCoupon">
        <view class="f-coupon-dot mb-4 w-full" />
        <view class="p-4">
          <coupon-card
            :data="{
              couponName,
              couponPrice,
              couponType,
              description,
              serverType: couponServerType,
              discount,
              expirySeconds,
              vendorCode,
            }"
          />
        </view>
      </template>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.f-coupon-dot {
  height: 50rpx;
  background-image: url('https://wx.gs1helper.com/images/p_coupon_o_o_o.png');
  background-repeat: no-repeat;
  background-position-y: 50%;
  background-size: contain;
}
</style>
