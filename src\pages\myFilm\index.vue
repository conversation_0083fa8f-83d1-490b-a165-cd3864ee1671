<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page -->
<route lang="jsonc">
{
  "layout": "tabbar",
  "style": {
    "navigationBarTitleText": "条码管理"
  }
}
</route>

<script lang="ts" setup>
import FreeDownPage from '@/components/myFilm/freeDownPage.vue'
import OrderDownPage from '@/components/myFilm/orderDownPage.vue'
import { Color } from '@/enums/colorEnum'

const tab = ref(0)
const list = [
  {
    name: '自由选择下载',
  },
  {
    name: '订单形式下载',
  },
]
</script>

<template>
  <up-tabs
    v-model:current="tab"
    class="bg-white"
    :line-color="Color.primary"
    :list="list"
    :line-width="90"
    :scrollable="false"
  />
  <free-down-page v-if="tab === 0" />
  <order-down-page v-if="tab === 1" />
</template>

<style lang="scss" scoped>
:deep(.u-tabs) {
  @apply bg-white;
}
</style>
