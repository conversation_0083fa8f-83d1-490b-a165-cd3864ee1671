import { defineStore } from 'pinia'
import { ref } from 'vue'

export const miniShopOrderStore = defineStore('miniShopOrderStore', () => {
  const orderCode = ref('')
  const tempName = ref('')
  const tempType = ref(3) // 1：按月收费，2：按季度收费，3：按年收费
  const capacity = ref(0)
  const flow = ref(0)
  const number = ref(0)

  return {
    orderCode,
    tempName,
    tempType,
    capacity,
    flow,
    number,
  }
})
