<script lang="ts" setup>
import { useFormTip } from '@/hooks/useFormTip'

const { isShowTip, tipContent, hideTip } = useFormTip()

defineExpose({
  isShowTip,
  tipContent,
  hideTip,
})
</script>

<template>
  <up-overlay :show="isShowTip" @click="hideTip">
    <view class="h-full center px-12">
      <view class="mt--20 rounded-md bg-white p-4" @tap.stop>
        <view class="font-bold">
          {{ tipContent.title }}
        </view>
        <view class="o-p mt-2 text-sm">
          {{ tipContent.content }}
        </view>
      </view>
    </view>
  </up-overlay>
</template>

  <style lang="scss" scoped></style>
