// @import './iconfont.css';

:root,
page {
  --fv-tabbar-height: 140rpx;

  // 修改按主题色
  --wot-color-theme: #165dff;
  --wot-color-success: #00b42a;
  --wot-color-warning: #ff7d00;
  --wot-color-danger: #dc3126;

  --o-body-bg-color: #f0f3f8;

  background-color: var(--o-body-bg-color);
  font-size: 14px;
  @extend %o-color-content;
}

.o-color-aid {
  color: #8c8c8c;
}

%o-color-content {
  color: #262626;
}

.o-color-content {
  @extend %o-color-content;
}

.o-bg-no {
  background: var(--o-body-bg-color);
}

.o-bg-white-disable {
  background: #dbdfe4;
}

.o-bg-primary-light {
  background: #e8efff;
}

.o-bg-primary {
  background: linear-gradient(91deg, #16abff 0%, #165dff 37.5%, #165dff 100%);
}

.o-bg-primary-disable {
  background: #94bfff;
}

.o-bg-transparent {
  background: transparent !important;
}

.o-dot {
  $w: 10rpx;
  width: $w;
  height: $w;
  margin-top: 0.35rem;
  background: var(--wot-color-theme);
  border-radius: calc($w / 2);
  flex-shrink: 0;
}

.o-line {
  height: 1px;
  background: rgba(0, 0, 0, 0.15);
  transform: scaleY(0.5);
}

.o-label-line {
  position: relative;

  &::after {
    position: absolute;
    right: 0;
    bottom: 3px;
    left: 0;
    height: 1px;
    content: '';
    background: #dadada;
    transform: scaleY(0.5);
  }
}

.o-shadow-blue-light {
  box-shadow: 0px 12rpx 26rpx -18rpx rgba(22, 92, 255, 0.6);
}

.o-shadow-blue {
  box-shadow: 0px 20rpx 26rpx -18rpx rgba(22, 92, 255, 1);
}

.o-tabbar-shadow {
  box-shadow:
    0 20rpx 40rpx -20rpx rgba(183, 210, 255, 0.8),
    0 0 10rpx rgba(183, 210, 255, 0.8);
}

.o-btn-no-style {
  padding-right: 0;
  padding-left: 0;
  margin-right: 0;
  margin-left: 0;
  background: none;

  &::after {
    border: none;
  }
}

.o-barcode-gray-card {
  padding: 0.2rem 0.5rem;
  background-color: rgba(220, 225, 234, 0.7);
}

.o-border {
  border: 1px solid $uni-border-color;
}

.o-border-gray-tag {
  background: var(--o-body-bg-color);
  border: 1px solid var(--o-body-bg-color);
}

.o-border-blue-tag {
  color: var(--wot-color-theme);
  background: #ebedff;
  border: 1px solid var(--wot-color-theme);
}

.o-p {
  line-height: 1.5;
  text-indent: 2em;
}

.o-logo {
  background-image: url('https://wx.gs1helper.com/images/logo_big.png');
  background-repeat: no-repeat;
  background-position: center center;
  background-size: contain;
}

.o-row-scroll {
  max-width: 100%;
  overflow-x: auto;
}

.o-cs-img {
  $w: 65rpx;
  width: $w !important;
  height: $w !important;
}

.o-cs-img-small {
  $w: 55rpx;
  width: $w;
  height: $w;
}

.o-customer-service-long {
  width: 100%;
  background-color: #fff;
  color: rgba(0, 0, 0, 0.85);
}

.o-tag {
  padding: 0 0.36rem;
  font-size: 0.7rem;
}

.o-pb {
  height: 290rpx;
}

.o-btn-light-bg-blue-border {
  position: relative;
  z-index: 0;
  border-radius: 16rpx;
  background: linear-gradient(118deg, #fff 1.55%, #f5f5f5 98.77%);
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    z-index: -1;
    inset: 0;
    border-radius: 16rpx;
    padding: 1px; // 边框宽度
    background: linear-gradient(91deg, #165dff 0%, #165dff 62.5%, #16abff 100%);
    -webkit-mask:
      linear-gradient(#fff 0 0) content-box,
      linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    pointer-events: none;
    opacity: 0.5;
  }
}

.o-form-require {
  &::after {
    content: '*';
    color: red;
  }
}

.o-form-underline {
  @apply py-1;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

//-----------------------------------

.o-vf-up-radio-group {
  .u-radio__label-wrap.radio--u-radio__label-wrap {
    @apply grow-1;
  }

  .u-radio__label-wrap {
    @apply grow-1;
  }
}

/*  #ifndef MP-TOUTIAO  */
.o-vf-up-radio-group {
  .u-radio {
    @apply w-full;

    margin-top: 0 !important;
    margin-bottom: 0 !important;

    &:nth-of-type(even) {
      @apply py-1;
      background-color: var(--o-body-bg-color);
    }
  }
}

/*  #endif  */
/*  #ifdef MP-TOUTIAO  */
.o-vf-radio-group {
  & > label:nth-of-type(even) {
    background-color: var(--o-body-bg-color);
  }
}

/*  #endif  */
/*  #ifndef MP-TOUTIAO  */
.o-vf-radio-group {
  & > Label:nth-of-type(even) {
    background-color: var(--o-body-bg-color);
  }
}

/*  #endif  */
