<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "进口商品报备"
  }
}
</route>

<script lang="ts" setup>
import CsLongButton from '@/components/customer/csLongButton.vue'
import { DESCRIPTION_STR } from '@/components/descriptionStr'
import ServerRadioGroup from '@/components/serverRadioGroup.vue'
import ServerTitle from '@/components/serverTitle.vue'
import { useCreateOrder } from '@/hooks/useCreateOrder'

const { loading, isShowStep, serveData, selectServe, descriptionServiceType, handleSubmit }
  = useCreateOrder({ orderConfirmUrl: '/pages/orderConfirm/default' })
</script>

<template>
  <ServerTitle />
  <view class="mt-3 px-4">
    <view class="flex flex-col items-center justify-center rd-2 bg-white p-4">
      <view v-if="isShowStep" class="mb-6 pt-2">
        <view class="mb-2 font-bold">
          报备说明：
        </view>
        <view
          v-for="(item, index) in DESCRIPTION_STR[descriptionServiceType]?.makeStep"
          :key="index"
          class="flex text-sm"
        >
          <view class="min-w-5 shrink-0 text-primary font-bold">
            {{ index + 1 }}.
          </view>
          <view>{{ item }}</view>
        </view>
      </view>
      <view class="o-color-aid flex items-center gap-3 text-sm" @click="isShowStep = !isShowStep">
        <up-icon name="question-circle" size="18" />
        <view>{{ isShowStep ? '隐藏' : '显示' }}报备说明</view>
        <up-icon v-if="isShowStep" name="arrow-up" size="16" />
        <up-icon v-else name="arrow-down" size="16" />
      </view>
    </view>
    <cs-long-button />
    <view class="mt-3 rd-2 bg-white p-4">
      <view class="mb-3 pt-4 font-bold">
        服务方案：
      </view>
      <view id="targetElement" class="mt-4">
        <server-radio-group v-model="selectServe" :items="serveData" />
      </view>
    </view>
    <view class="o-color-aid mt-4 text-center text-xs">
      <text class="pr-2 text-red-500">
        *
      </text>
      更多数量请联系客服
    </view>
    <view class="p-10" />
    <view class="fixed bottom-0 left-0 z-10 box-border w-full p-4">
      <view
        class="flex flex-grow-1 items-center justify-center rd-2 p-3 text-white font-bold"
        :class="loading ? 'o-bg-primary-disable' : 'o-bg-primary o-shadow-blue'"
        @click="handleSubmit"
      >
        提交方案
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.f-label {
  flex-shrink: 0;
  width: 33vw;
}

.f-w {
  bottom: -0.8rem;
  left: 100rpx;
}

.f-h {
  top: 70rpx;
  left: -2.5rem;
  transform: rotate(-90deg);
}

:deep(.f-table) {
  // border-bottom: 1px solid #e4e4e4;
  &:nth-of-type(even) {
    background-color: var(--o-body-bg-color);
  }
}

.f-serve {
  width: 540rpx;
}
</style>
