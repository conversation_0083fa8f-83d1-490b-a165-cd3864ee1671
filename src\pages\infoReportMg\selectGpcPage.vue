<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "选择产品分类"
  }
}
</route>

<script lang="ts" setup>
import type { ReportGpcPageParams, ReportGpcPageRes } from '@/service/infoReportApi'
import debounce from 'debounce'
import { storeToRefs } from 'pinia'
import { Color } from '@/enums/colorEnum'
import { reportGpcPageApi } from '@/service/infoReportApi'
import { infoReportEditStore } from '@/store/infoReportEditStore'

const useInfoReportEditStore = infoReportEditStore()
const { activeGpcItem } = storeToRefs(useInfoReportEditStore)
const keyword = ref('')
const loading = ref(false)
const dataList = ref<ReportGpcPageRes['data']>([])
const pageIndex = ref(1)
const level = ref(1)
const hasMore = ref(true)
const PAGE_SIZE = 50

const handleInput = debounce(() => {
  handleSearch()
}, 800)

function handleSearch() {
  // 重置分页数据
  pageIndex.value = 1
  dataList.value = []
  hasMore.value = true
  getGpcData()
}

function handleSelect(dataItem: ReportGpcPageRes['data'][0]) {
  if (dataItem.level === 4) {
    // 回退回infoReportDetailFromPage页面
    activeGpcItem.value = {
      code: dataItem.gpcType,
      gpcTypeName: dataItem.gpcTypeName,
    }
    // 获取当前页面栈
    const pages = getCurrentPages()
    // 找到infoReportDetailFromPage页面的索引
    let targetIndex = -1
    for (let i = pages.length - 1; i >= 0; i--) {
      if (pages[i].route.includes('infoReportDetailFromPage')) {
        targetIndex = i
        break
      }
    }

    if (targetIndex !== -1) {
      // 计算需要回退的层数
      const delta = pages.length - 1 - targetIndex
      uni.navigateBack({ delta })
    }
    else {
      // 如果找不到目标页面，直接回退一层
      uni.navigateBack()
    }
  }
  else {
    console.log('🚀 ~ handleSelect ~ dataItem:', dataItem)
    uni.navigateTo({
      url: `/pages/infoReportMg/selectGpcPage?code=${dataItem.gpcType}&pid=${dataItem.id}`,
    })
  }
}

function getGpcData(pid?: number) {
  if (!hasMore.value)
    return

  loading.value = true

  const params: ReportGpcPageParams = {
    pageIndex: pageIndex.value,
    pageSize: PAGE_SIZE,
  }
  if (keyword.value !== '') {
    params.gpcTypeName = keyword.value
  }
  else {
    if (pid) {
      params.pid = pid
    }
    else {
      params.level = level.value
    }
  }

  reportGpcPageApi(params).then((res) => {
    if (pageIndex.value === 1) {
      // 首次加载或搜索时替换数据
      dataList.value = res.data
    }
    else {
      // 加载更多时追加数据
      dataList.value = [...(dataList.value || []), ...res.data]
    }

    // 判断是否还有更多数据
    hasMore.value = res.data.length === PAGE_SIZE

    if (hasMore.value) {
      pageIndex.value++
    }
  }).finally(() => {
    loading.value = false
  })
}

function scrolltolower() {
  if (loading.value || !hasMore.value)
    return
  getGpcData()
}

function toNationalGpcPage() {
  uni.navigateTo({
    url: `/pages/infoReportMg/selectNationalGpcPage?level=1`,
  })
}

onLoad((option) => {
  if (option.pid) {
    getGpcData(Number(option.pid))
  }
  else {
    getGpcData()
  }
})
</script>

<template>
  <view>
    <view
      class="f-search-bar fixed left-0 top-0 z-1 box-border w-full flex items-center bg-white px-3 py-2 shadow-blue"
    >
      <view class="o-bg-no flex flex-1 items-center gap-2 py-1 pl-4 pr-3" style="border-radius: 2rem">
        <up-icon :color="Color.gray" name="search" size="20" />
        <up-input
          v-model="keyword" :maxlength="30" border="none" class="o-bg-transparent grow" clearable
          placeholder="请输入产品分类名称搜索" @change="handleInput"
        />
      </view>
    </view>
    <up-list class="box-border px-6 pt-14" :enable-back-to-top="true" @scrolltolower="scrolltolower">
      <view
        v-if="level === 1" class="mb-2 flex justify-between rounded bg-red-100 px-4 py-2 text-red-600"
        @click="toNationalGpcPage"
      >
        <view>
          国补分类点这里
        </view>
        <up-icon name="arrow-right" :color="Color.red" size="12" />
      </view>
      <up-list-item v-for="(item) in dataList" :key="item.id">
        <view class="f-item flex flex-wrap items-baseline py-2 space-x-1" @click="handleSelect(item)">
          <view v-if="item.level !== 1" class="flex items-baseline">
            <text class="text-xs text-gray-400">
              ...
            </text>
            <up-icon name="arrow-right" color="#9ca3af" size="12" />
          </view>
          <view class="rounded bg-white px-3 py-1">
            {{ item.gpcTypeName }}
          </view>
          <view v-if="item.level !== 4" class="flex items-baseline">
            <up-icon name="arrow-right" color="#9ca3af" size="12" />
            <text class="text-xs text-gray-400">
              ...
            </text>
          </view>
        </view>
      </up-list-item>

      <!-- 加载状态提示 -->
      <view v-if="loading" class="flex justify-center py-4">
        <up-loading-icon mode="semicircle" />
        <text class="ml-2 text-gray-500">加载中...</text>
      </view>

      <!-- 没有更多数据提示 -->
      <view v-else-if="!hasMore" class="flex justify-center py-8">
        <text class="text-gray-400">没有更多数据了</text>
      </view>
    </up-list>
  </view>
</template>

<style lang="scss" scoped>
.f-item {
  border-bottom: 1px solid #eee;
}
</style>
