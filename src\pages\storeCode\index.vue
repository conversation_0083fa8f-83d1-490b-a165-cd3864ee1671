<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "店内条码制作"
  }
}
</route>

<script lang="ts" setup>
import type { CreateOrderParams, CreateOrderRes } from '@/service/orderApi'
import { storeToRefs } from 'pinia'
import { DESCRIPTION_STR, PROJECT_ALL } from '@/components/descriptionStr'
import ServerTitle from '@/components/serverTitle.vue'
import { BarType, ServerType } from '@/enums'
import { Color } from '@/enums/colorEnum'
import { useToPath } from '@/hooks/useToPath'
import { createOrderApi } from '@/service/orderApi'
import { orderStore } from '@/store/orderStore'
import { useServiceStore } from '@/store/serviceStore'
import { useUserStore } from '@/store/user'

const { toPath } = useToPath()

type ErrorTypeColor = typeof Color.inputColor | typeof Color.red

const serviceStore = useServiceStore()
const { descriptionServiceType } = storeToRefs(serviceStore)

const useOrderStore = orderStore()
const { vendorCode: storeVendorCode, orderCode, serverType } = storeToRefs(useOrderStore)

const userStore = useUserStore()
const { userId } = storeToRefs(userStore)
userStore.login()

const LABEL_WIDTH = 80
const MAX_SIZE = 500 // 每次允许最大生成数
const loading = ref(false)
const vendorCode = ref<string>('20') // 前缀20~24
const isShowStep = ref(false)
const isShowVendorCodePick = ref(false)

const projectCode = reactive({
  from: '',
  to: '',
})
const errorTypeColor = reactive<{
  vendorCode: ErrorTypeColor
  from: ErrorTypeColor
  to: ErrorTypeColor
  packingCode: ErrorTypeColor
}>({
  vendorCode: Color.inputColor,
  from: Color.inputColor,
  to: Color.inputColor,
  packingCode: Color.inputColor,
})

function handleSubmit() {
  autocomplete('from')
  if (projectCode.to !== '') {
    autocomplete('to')
  }

  if (projectCode.from.length < 0 || projectCode.from.length > 10) {
    uni.showToast({
      icon: 'none',
      title: '项目代码为10位数',
      duration: 2000,
    })
    errorTypeColor.from = Color.red
    return
  }
  else {
    if (projectCode.to.length > 0) {
      if (projectCode.to.length !== projectCode.from.length) {
        uni.showToast({
          icon: 'none',
          title: '项目代码位数前后要相同',
          duration: 2000,
        })
        errorTypeColor.to = Color.red
        return
      }
      else {
        if (Number(projectCode.to) < Number(projectCode.from)) {
          uni.showToast({
            icon: 'none',
            title: '项目代码需要从小到大',
            duration: 2000,
          })
          errorTypeColor.to = Color.red
          return
        }
      }
    }
  }
  const codeLength = Number(projectCode.to) - Number(projectCode.from) + 1
  if (codeLength > MAX_SIZE) {
    uni.showToast({
      icon: 'none',
      title: `单次最多生成${MAX_SIZE}条条码，请重新输入项目代码`,
      duration: 3000,
    })
    errorTypeColor.to = Color.red
    return
  }
  let startProjectCode = projectCode.to
  if (projectCode.to === '') {
    startProjectCode = projectCode.from
  }
  const params: CreateOrderParams = {
    serverType: ServerType.storeCode,
    barType: BarType.EAN13,
    fromTo: import.meta.env.VITE_FROM_PLATFORM,
    startProjectCode: projectCode.from,
    endProjectCode: startProjectCode,
    size: 1,
    userId: userId.value,
    vendorCode: vendorCode.value,
  }
  loading.value = true
  uni.showLoading({ title: '生成预览中...' })
  createOrderApi(params)
    .then((res: CreateOrderRes) => {
      // 厂商识别代码，用于支付时筛选优惠券
      storeVendorCode.value = vendorCode.value
      orderCode.value = res.data.orderCode
      serverType.value = ServerType.storeCode
      loading.value = false
      uni.hideLoading()
      uni.navigateTo({
        url: '/pages/orderConfirm/storeCode',
      })
    })
    .catch(() => {
      loading.value = false
      uni.hideLoading()
    })
}

function autocomplete(where: 'from' | 'to') {
  if (projectCode[where].length < 10) {
    const padding = '0'.repeat(10 - projectCode[where].length)
    projectCode[where] = padding + projectCode[where]
  }
}

function toPathFunc() {
  if (!loading.value) {
    const serverData = PROJECT_ALL.find(item => item.descriptionServiceType === 'labelPrint')
    toPath(serverData, 'redirectTo')
  }
}

function vendorCodeConfirm(e: any) {
  vendorCode.value = e.value[0]
  isShowVendorCodePick.value = false
}
</script>

<template>
  <ServerTitle />
  <view class="mt-3 px-4">
    <view class="flex flex-col items-center justify-center rd-2 bg-white p-4">
      <view v-if="isShowStep" class="mb-6 pt-2">
        <view class="mb-2 font-bold">
          制作步骤：
        </view>
        <view
          v-for="(item, index) in DESCRIPTION_STR[descriptionServiceType].makeStep"
          :key="index"
          class="flex text-sm"
        >
          <view class="min-w-5 shrink-0 text-primary font-bold">
            {{ index + 1 }}.
          </view>
          <view>{{ item }}</view>
        </view>
      </view>
      <view class="o-color-aid flex items-center gap-3 text-sm" @click="isShowStep = !isShowStep">
        <up-icon name="question-circle" size="18" />
        <view>{{ isShowStep ? '隐藏' : '显示' }}条码制作说明</view>
        <up-icon v-if="isShowStep" name="arrow-up" size="16" />
        <up-icon v-else name="arrow-down" size="16" />
      </view>
    </view>
    <view
      class="f-red-card my-2 flex items-center justify-between rd-1 px-2 py-1 text-sm text-red-500"
      @click="toPathFunc"
    >
      <text>直接印刷标签贴纸，点这里</text>
      <up-icon name="arrow-right" size="14" />
    </view>
    <view class="mt-3 rd-2 bg-white p-4">
      <view class="mb-3 pt-4 font-bold">
        要生成条码的编码：
      </view>
      <up-form :label-width="LABEL_WIDTH">
        <up-form-item label="任意前缀" @click="isShowVendorCodePick = true">
          <up-input v-model="vendorCode" border="bottom" disabled disabled-color="#fff" />
          <template #right>
            <up-icon name="arrow-right" size="14" />
          </template>
        </up-form-item>
        <up-picker
          :columns="[['20', '21', '22', '23', '24']]"
          :show="isShowVendorCodePick"
          :confirm-color="Color.primary"
          close-on-click-overlay
          title="任意前缀"
          @cancel="isShowVendorCodePick = false"
          @close="isShowVendorCodePick = false"
          @confirm="vendorCodeConfirm"
        />
      </up-form>
      <up-text text="项目代码" />
      <view class="flex items-center">
        <up-input
          v-model="projectCode.from"
          :color="errorTypeColor.from"
          :maxlength="10"
          placeholder="0000000000"
          border="bottom"
          clearable
          type="number"
          @blur="autocomplete('from')"
          @focus="errorTypeColor.from = Color.inputColor"
        >
          <template #suffix>
            <text class="text-xs color-gray">
              {{ projectCode.from.length }}/10
            </text>
          </template>
        </up-input>
        <view>~</view>
        <up-input
          v-model="projectCode.to"
          :color="errorTypeColor.to"
          :maxlength="10"
          placeholder="999999999"
          border="bottom"
          clearable
          type="number"
          @blur="autocomplete('to')"
          @focus="errorTypeColor.to = Color.inputColor"
        >
          <template #suffix>
            <text class="text-xs color-gray">
              {{ projectCode.to.length }}/10
            </text>
          </template>
        </up-input>
      </view>
      <up-form :label-width="LABEL_WIDTH">
        <up-form-item label="检验码">
          <up-input
            placeholder="自动生成，无须输入"
            border="bottom"
            disabled
            disabled-color="#fff"
          />
        </up-form-item>
      </up-form>
    </view>
    <view class="p-10" />
    <view class="fixed bottom-0 left-0 z-10 box-border w-full p-4">
      <view
        class="flex flex-grow-1 items-center justify-center rd-2 p-3 text-white font-bold"
        :class="loading ? 'o-bg-primary-disable' : 'o-bg-primary o-shadow-blue'"
        @click="handleSubmit"
      >
        生成预览
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.f-red-card {
  border: 1px dashed var(--wot-color-danger);
  background-color: rgba(245, 63, 63, 0.05);
}
</style>
