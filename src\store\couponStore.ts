import type { CouponPageResData } from '@/service/orderApi'
import { defineStore } from 'pinia'
import { ref } from 'vue'

const couponStore = defineStore('couponStore', () => {
  // 用于存储优惠券列表，在makeFilm请求了，缓存到下一页
  const couponList = ref<CouponPageResData[]>([])
  const bestDiscountItem = ref<CouponPageResData>()

  return {
    couponList,
    bestDiscountItem,
  }
})

export const useCouponStore = couponStore()
