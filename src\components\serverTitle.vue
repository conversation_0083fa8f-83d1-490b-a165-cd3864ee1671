<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { DESCRIPTION_STR } from '@/components/descriptionStr'
import { useServiceStore } from '@/store/serviceStore'

const serviceStore = useServiceStore()
const { descriptionServiceType } = storeToRefs(serviceStore)
</script>

<template>
  <view :class="DESCRIPTION_STR[descriptionServiceType].colorClass" class="pl-2">
    <view class="sf-title-round pb-2 pl-3 pt-4">
      <view class="sf-title-text text-2xl font-bold">
        {{ DESCRIPTION_STR[descriptionServiceType].title }}
      </view>
      <view class="o-color-aid">
        {{ DESCRIPTION_STR[descriptionServiceType].typeStr }}
      </view>
      <view class="text-sm">
        {{ DESCRIPTION_STR[descriptionServiceType].description }}
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss"></style>
