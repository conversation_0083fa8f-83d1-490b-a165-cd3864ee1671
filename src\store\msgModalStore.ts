import { defineStore } from 'pinia'
import { ref } from 'vue'

interface MsgModalOption {
  title?: string
  content?: string
  confirmText?: string
  cancelText?: string
  closeOnClickOverlay?: boolean
}

export const msgModalStore = defineStore('msgModalStore', () => {
  const isGlobalModalShow = ref(false)
  const title = ref('')
  const content = ref('')
  const confirmText = ref('确认')
  const cancelText = ref('取消')
  const resolveConfirm = ref()
  const rejectCancel = ref()
  const showCancelButton = ref(false)
  const closeOnClickOverlay = ref(true)

  const msgModalShow = (msgOption: MsgModalOption, method: 'alert' | 'confirm') =>
    new Promise((resolve, reject) => {
      isGlobalModalShow.value = true
      title.value = msgOption.title
      content.value = msgOption.content
      if (msgOption.closeOnClickOverlay === false) {
        closeOnClickOverlay.value = false
      }
      confirmText.value = msgOption.confirmText || '确认'
      resolveConfirm.value = resolve
      if (method === 'confirm') {
        showCancelButton.value = true
        cancelText.value = msgOption.cancelText || '取消'
        rejectCancel.value = reject
      }
    })

  // 打开Alert 弹框
  const alert = (msgOption: MsgModalOption) => {
    return msgModalShow(msgOption, 'alert')
  }
  // 打开Confirm 弹框
  const confirm = (msgOption: MsgModalOption) => {
    return msgModalShow(msgOption, 'confirm')
  }

  const handleConfirm = () => {
    isGlobalModalShow.value = false
    if (resolveConfirm.value) {
      resolveConfirm.value() // 调用resolve，返回Promise
    }
  }

  const handleCancel = () => {
    isGlobalModalShow.value = false
    if (rejectCancel.value) {
      rejectCancel.value() // 调用reject，返回Promise
    }
  }

  return {
    isGlobalModalShow,
    title,
    content,
    confirmText,
    showCancelButton,
    closeOnClickOverlay,
    alert,
    confirm,
    handleConfirm,
    handleCancel,
  }
})
