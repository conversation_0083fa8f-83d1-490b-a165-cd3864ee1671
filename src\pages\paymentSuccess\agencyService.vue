<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "支付成功"
  }
}
</route>

<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import ModifyStep from '@/components/agencyService/modifyStep.vue'
import RegisterStep from '@/components/agencyService/registerStep.vue'
import RenewalStep from '@/components/agencyService/renewalStep.vue'
import CouponCard from '@/components/Price/CouponCard.vue'
import PriceBox from '@/components/Price/PriceBox.vue'
import { ServerType } from '@/enums'
import { getCouponApi } from '@/service/orderApi'
import { orderStore } from '@/store/orderStore'

const useOrderStore = orderStore()
const {
  orderCode,
  tempName,
  actuallyPrice,
  serverType: storeServerType,
} = storeToRefs(useOrderStore)

const couponName = ref('')
const couponPrice = ref(0)
const couponType = ref(2)
const description = ref('')
const discount = ref(0)
const serverType = ref<ServerType>()
const expirySeconds = ref(0)
const vendorCode = ref('')
const showCoupon = ref(false)

getCouponApi({ orderCode: orderCode.value }).then((res) => {
  const d = res.data
  if (d?.couponId) {
    couponName.value = d.couponName
    couponPrice.value = d.couponPrice
    couponType.value = d.couponType
    description.value = d.description
    discount.value = d.discount
    serverType.value = d.serverType
    expirySeconds.value = d.expirySeconds
    vendorCode.value = d.vendorCode
    showCoupon.value = true
  }
})

function toHandlingService() {
  uni.switchTab({
    // 这样才有后退打底
    url: '/pages/userPage/index',
    success: () => {
      uni.navigateTo({
        url: `/pages/myAgency/index?serverType=${storeServerType.value}&skipAuth=1`,
      })
    },
  })
}
</script>

<template>
  <view class="px-4 pb-10 pt-4">
    <view class="pl-3 text-xl text-primary font-bold">
      订单支付成功！
    </view>
    <view v-if="showCoupon" class="pl-3 text-sm text-primary">
      恭喜获得一张折扣券
    </view>
    <view class="mt-2 rd-2 bg-white p-4">
      <view class="o-color-aid text-xs">
        订单编号：{{ orderCode }}
      </view>
      <view class="o-color-aid mt-2 text-xs">
        服务类型：
      </view>
      <view class="mb-2 flex items-center">
        {{ tempName }}
      </view>
      <view class="flex items-baseline justify-end">
        <view class="text-sm">
          实付：
        </view>
        <price-box :price="actuallyPrice" :size="48" />
      </view>
    </view>
    <!--    <NeedPhoneForm /> -->
    <view class="mt-3 rd-2 bg-white">
      <view class="p-4">
        <RegisterStep v-if="storeServerType === ServerType.registerService" />
        <RenewalStep v-if="storeServerType === ServerType.renewalService" />
        <ModifyStep v-if="storeServerType === ServerType.modifyService" />
        <view
          class="o-bg-primary o-shadow-blue mt-4 flex flex-grow-1 items-center justify-center rd-2 p-3 text-white font-bold"
          @click="toHandlingService"
        >
          前往提交资料
        </view>
      </view>
      <template v-if="showCoupon">
        <view class="f-coupon-dot mb-4 w-full" />
        <view class="p-4">
          <coupon-card
            :data="{
              couponName,
              couponPrice,
              couponType,
              description,
              serverType,
              discount,
              expirySeconds,
              vendorCode,
            }"
          />
        </view>
      </template>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.f-coupon-dot {
  height: 50rpx;
  background-image: url('https://wx.gs1helper.com/images/p_coupon_o_o_o.png');
  background-repeat: no-repeat;
  background-position-y: 50%;
  background-size: contain;
}
</style>
