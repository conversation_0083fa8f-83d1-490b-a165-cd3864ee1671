export function jumpToWeChatCustomerService() {
  openWeChatCustomerService(
    'https://work.weixin.qq.com/kfid/kfca8565b71565e361f',
    'ww00c8052fbcfe3258',
  )
}

// 打开微信客服
function openWeChatCustomerService(weiXinCustomerServiceUrl = '', corpId = '', showMessageCard = false, sendMessageTitle = '', sendMessagePath = '', sendMessageImg = '') {
  if (!weiXinCustomerServiceUrl || !corpId) {
    console.log('请配置好客服链接或者企业ID')
  }
  else {
    wx.openCustomerServiceChat({
      // 客服信息
      extInfo: {
        url: weiXinCustomerServiceUrl, // 客服链接 https://work.weixin.qq.com/xxxxxxxx
      },
      corpId, // 企业ID wwed1ca4d3597eXXXX
      showMessageCard, // 是否发送小程序气泡消息
      sendMessageTitle, // 气泡消息标题
      sendMessagePath, // 气泡消息小程序路径（一定要在小程序路径后面加上“.html”，如：pages/index/index.html）
      sendMessageImg, // 气泡消息图片
      success(res) {
        console.log('success', JSON.stringify(res))
      },
      fail(err) {
        console.log('fail', JSON.stringify(err))
        /*        return wx.showToast({
          title: err.errMsg,
          icon: 'none',
        }) */
      },
    })
  }
}
