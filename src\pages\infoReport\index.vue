<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "产品编码信息通报"
  }
}
</route>

<script lang="ts" setup>
import type {
  CreateReportOrderParams,
  PriceTempListResData,
} from '@/service/orderApi'
import debounce from 'debounce'
import { storeToRefs } from 'pinia'
import CsLongButton from '@/components/customer/csLongButton.vue'
import QuickReportStep from '@/components/infoReport/quickReportStep.vue'
import ReportStep from '@/components/infoReport/reportStep.vue'
import ServerTitle from '@/components/serverTitle.vue'
import { ServerType } from '@/enums'
import { Color } from '@/enums/colorEnum'
import {
  createReportOrderApi,
  priceTempListApi,

} from '@/service/orderApi'
import { msgModalStore } from '@/store/msgModalStore'
import { orderStore } from '@/store/orderStore'
import { useUserStore } from '@/store/user'

const useMsgModalStore = msgModalStore()

const useOrderStore = orderStore()
const { orderCode, serverType } = storeToRefs(useOrderStore)
const userStore = useUserStore()

const loading = ref(false)
const serveDataInfoReport = ref<PriceTempListResData[]>([])
const serveDataQuickReport = ref<PriceTempListResData[]>([])
const priceTempId = ref<number>()

const handleSubmit = debounce(
  () => {
    if (!priceTempId.value) {
      useMsgModalStore.alert({ title: '请选择方案' }).then(() => {
        uni.pageScrollTo({ selector: '#targetElement' })
      })
      return
    }
    loading.value = true
    uni.showLoading({ title: '订单提交中...' })
    const params: CreateReportOrderParams = {
      priceTempId: priceTempId.value,
      serverType: serverType.value,
      userId: useUserStore().userId,
    }
    params.fromTo = import.meta.env.VITE_FROM_PLATFORM


    createReportOrderApi(params)
      .then((res: any) => {
        orderCode.value = res.data.orderCode
        loading.value = false
        uni.hideLoading()
        uni.navigateTo({
          url: '/pages/orderConfirm/infoReport',
        })
      })
      .catch(() => {
        loading.value = false
        uni.hideLoading()
      })
  },
  1000,
  { immediate: true },
)

function handleChange(evt: any) {
  const [type, id] = evt.detail.value.split(',')
  priceTempId.value = Number(id)
  if (type === 'quickReport') {
    serverType.value = ServerType.quickReport
  }
  else {
    serverType.value = ServerType.infoReport
  }
}

onLoad(() => {
  userStore.login().then(() => {
    priceTempListApi({ tempType: ServerType.infoReport }).then((res) => {
      serveDataInfoReport.value = res.data
    })
    priceTempListApi({ tempType: ServerType.quickReport }).then((res) => {
      serveDataQuickReport.value = res.data
    })
  })
})
</script>

<template>
  <ServerTitle />
  <view class="px-4">
    <view class="mt-3 rd-2 bg-white p-4">
      <view class="mb-3 pt-3 font-bold">
        服务方案：
      </view>
      <view id="targetElement" class="mt-4">
        <radio-group class="o-vf-radio-group" @change="handleChange">
          <view class="mb-3 text-gray-500 font-bold">
            逐个产品通报：
          </view>
          <label v-for="(item, index) in serveDataQuickReport" :key="index" class="flex py-1">
            <radio
              style="transform: scale(0.7)" :color="Color.primary" :value="`quickReport,${item.priceTempId}`"
              :checked="item.priceTempId === priceTempId"
            />
            <view class="f-serve flex justify-between">
              <view class="f-server-name flex-wrap pr-2 space-x-1">
                <text>{{ item.tempName }}</text>
                <up-tag
                  v-if="item.label" :text="item.label" size="mini"
                  style="transform-origin: left center; scale: 0.7" type="error"
                />
              </view>
              <view class="shrink-0">
                {{ item.price }}
                <text>{{ item.tempUnit }}</text>
              </view>
            </view>
          </label>
          <view class="mb-3 mt-5 text-gray-500 font-bold">
            整批产品通报：
          </view>
          <label v-for="(item, index) in serveDataInfoReport" :key="index" class="flex py-1">
            <radio
              style="transform: scale(0.7)" :color="Color.primary" :value="`infoReport,${item.priceTempId}`"
              :checked="item.priceTempId === priceTempId"
            />
            <view class="f-serve flex justify-between">
              <view class="f-server-name flex-wrap pr-2 space-x-1">
                <text>{{ item.tempName }}</text>
                <up-tag
                  v-if="item.label" :text="item.label" size="mini"
                  style="transform-origin: left center; scale: 0.7" type="error"
                />
              </view>
              <view class="shrink-0">
                {{ item.price }}
                <text>{{ item.tempUnit }}</text>
              </view>
            </view>
          </label>
        </radio-group>
      </view>
      <view class="o-color-aid mt-4 text-center text-xs">
        <text class="pr-2 text-red-500">
          *
        </text>
        更多数量请分多次订单
      </view>
    </view>
    <cs-long-button />
    <view class="mt-3 flex flex-col items-center justify-center rd-2 bg-white p-4">
      <view class="mb-6 pt-2">
        <view class="mb-2 font-bold">
          <text class="text-primary">逐个</text>跟<text class="text-primary">整批</text>通报的区别
        </view>
        <view class="flex text-sm">
          <view class="min-w-5 shrink-0 text-primary font-bold">
            1.
          </view>
          <view><text class="text-primary">整批</text>通报：通过电脑上传Excel表格通报；<text class="text-primary">逐个</text>通报：在本小程序填写。</view>
        </view>
        <view class="flex text-sm">
          <view class="min-w-5 shrink-0 text-primary font-bold">
            2.
          </view>
          <view><text class="text-primary">整批</text>通报：需凑齐产品才上传表格，按批算次数；<text class="text-primary">逐个</text>通报：通报一个算一个，更灵活。</view>
        </view>
      </view>
      <quick-report-step />
      <report-step />
    </view>
    <view class="p-10" />
    <view class="fixed bottom-0 left-0 z-10 box-border w-full p-4">
      <view
        :class="loading ? 'o-bg-primary-disable' : 'o-bg-primary o-shadow-blue'"
        class="flex flex-grow-1 items-center justify-center rd-2 p-3 text-white font-bold" @click="handleSubmit"
      >
        提交方案
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.f-serve {
  $w: 560rpx;
  min-width: $w;
  max-width: $w;
}

.f-server-name {
  min-width: 0;
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: normal !important;
}
</style>
