<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "业务",
    "navigationStyle": "custom"
  }
}
</route>

<script lang="ts" setup>
import type {
  MallGoodsLoadRes,
  OrderCreateParams,
  SkuListData,
} from '@/service/shoppingMallApi'
import CsBtnLeftButton from '@/components/customer/csBtnLeftButton.vue'
import MallSwiper from '@/components/mallSwiper.vue'
import { wxRequestPayment } from '@/components/mix'
import PriceBox from '@/components/Price/PriceBox.vue'
import { FromPlatform, ServerType } from '@/enums'
import { addressPageApi } from '@/service/addressPageApi'
import {
  mallGoodsLoadApi,
  orderCreateApi,
  payMallOrderApi,
  submitMallOrderApi,
} from '@/service/shoppingMallApi'
import { msgModalStore } from '@/store/msgModalStore'
import { useUserStore } from '@/store/user'

// 获取屏幕边界到安全区域距离
const { safeAreaInsets } = uni.getSystemInfoSync()
const { windowWidth, windowHeight } = uni.getSystemInfoSync()

const useMsgModalStore = msgModalStore()

const userStore = useUserStore()

const loading = ref(false)
const mallData = ref<MallGoodsLoadRes['data']>()
const carousesList = ref<string[]>([]) // 主图 + 轮播图
const selectArr = ref<string[]>([])
const isSheetShow = ref(false)
const isShowPreviewImg = ref(false)
const quantity = ref(1)
const previewImgUrl = ref<string>()
const allGoodsImg = ref<string[]>([])
const imgCurrent = ref(0)
const activeSkuItem = ref<SkuListData>()
let goodsId = 0

const addressData = reactive({
  addressDetail: '',
  district: '',
  phone: '',
  realName: '',
})

onLoad((option) => {
  goodsId = Number(option.goodsId)
  // 预防首页太快没登录，这里再自动判断，没登录再登录
  userStore.login().then(() => {
    mallGoodsLoadApi(goodsId).then((res) => {
      mallData.value = res.data
      carousesList.value = [res.data.imageUrl, ...res.data.sliderImages.split(',')]
      // activeSkuItem.value = res.data.skuList[0]
      selectArr.value = res.data.skuList[0].specValueNameStr.split(',')
      // getPreviewImgAndPrice()
      getAllGoodsImg()
    })
  })
})

onShow(() => {
  getAddressData()
})

function getAddressData() {
  addressPageApi({
    userId: userStore.userId,
  }).then((res) => {
    if (res.data && res.data.length > 0) {
      const d = res.data.filter(item => item.isDefault)
      if (d.length > 0) {
        addressData.addressDetail = d[0].addressDetail
        addressData.district = d[0].district
        addressData.phone = d[0].phone
        addressData.realName = d[0].realName
      }
      else {
        addressData.addressDetail = res.data[0].addressDetail
        addressData.district = res.data[0].district
        addressData.phone = res.data[0].phone
        addressData.realName = res.data[0].realName
      }
    }
  })
}

// TODO 测试库存为0情况
function handleSubmit() {
  if (loading.value || activeSkuItem.value?.stock === 0) {
    return
  }

  const okFunc = () => {
    loading.value = false
    uni.hideLoading()
  }

  if (addressData.phone) {
    loading.value = true
    uni.showLoading({ title: '提交订单中...' })
    const params: OrderCreateParams = {
      attrDetailValueId: activeSkuItem.value.attrDetailValueId,
      goodsId,
      number: quantity.value,
      serverType: ServerType.labelPrint,
      userId: useUserStore().userId,
    }
    // #ifdef MP-WEIXIN
    params.fromTo = import.meta.env.VITE_FROM_PLATFORM
    // #endif
    // #ifdef MP-TOUTIAO
    params.fromTo = FromPlatform.douYin
    // #endif
    orderCreateApi(params)
      .then((orderCodeRes) => {
        submitMallOrderApi({
          addressDetail: addressData.addressDetail,
          contact: addressData.realName,
          contactPhone: addressData.phone,
          district: addressData.district,
          // couponId: 0,
          // email: '',
          orderCode: orderCodeRes.data.orderCode,
        })
          .then((submitRes) => {
            payMallOrderApi({
              fromTo: import.meta.env.VITE_FROM_PLATFORM,
              orderCode: submitRes.data.orderCode,
            })
              .then((payRes) => {
                const url = '/pages/paymentSuccess/shoppingMall'
                if (payRes.data.isNeedToPay) {
                  wxRequestPayment(payRes.data)
                    .then(() => {
                      okFunc()
                      // TODO 这里只是关闭了一个页面，符不符合逻辑？后退是否对store传参有影响？
                      uni.redirectTo({
                        url,
                      })
                    })
                    .catch(() => {
                      okFunc()
                    })
                }
                else {
                  uni.redirectTo({
                    url,
                  })
                }
              })
              .catch(() => {
                okFunc()
              })
          })
          .catch(() => {
            okFunc()
          })
      })
      .catch(() => {
        okFunc()
      })
  }
  else {
    useMsgModalStore.confirm({
      title: '温馨提示',
      content: '请先添加地址',
    })
  }
}

function isSelectTag(index: number, specValue: string) {
  if (selectArr.value.length === 0) {
    return false
  }
  return selectArr.value[index] === specValue
}

function handleSelectTag(index: number, specValue: string, length: number) {
  if (selectArr.value.length === 0) {
    for (let i = 0; i < length; i++) {
      selectArr.value.push('')
    }
  }
  selectArr.value[index] = specValue
  imgCurrent.value = mallData.value.skuList?.findIndex(
    item => item.specValueNameStr === selectArr.value.join(','),
  )
  activeSkuItem.value = mallData.value.skuList[imgCurrent.value]
  getPreviewImgAndPrice()
}

function getPreviewImgAndPrice() {
  previewImgUrl.value = activeSkuItem.value.goodsImage ?? mallData.value.imageUrl
  // price.value = activeSkuItem.value.originalPrice * quantity.value
}

function getAllGoodsImg() {
  mallData.value.skuList?.forEach((item) => {
    if (item.goodsImage) {
      allGoodsImg.value.push(item.goodsImage)
    }
    else {
      allGoodsImg.value.push(mallData.value.imageUrl)
    }
  })
}

function initSelectSkuItem() {
  activeSkuItem.value = mallData.value.skuList[0]
  getPreviewImgAndPrice()
}

function showSelectSheet() {
  if (!activeSkuItem.value) {
    initSelectSkuItem()
  }
  isSheetShow.value = true
}

function closeSheetShow() {
  isSheetShow.value = false
}

function toPreviewListImg() {
  imgCurrent.value = mallData.value.skuList?.findIndex(
    item => item.attrDetailValueId === activeSkuItem.value.attrDetailValueId,
  )
  isShowPreviewImg.value = true
}

function closePreviewShow() {
  isShowPreviewImg.value = false
}

function getSelectTagName(arr: string[]) {
  // 将数组转为字符串
  return arr.join(' - ')
}

const activeSkuName = computed(() => {
  return selectArr.value.join('，')
})

function onChangePreviewImg(onChangeData: {
  current: number
  currentItemId: string
  source: 'autoplay' | 'touch' | 'nav'
}) {
  activeSkuItem.value = mallData.value.skuList[onChangeData.current]
  selectArr.value = mallData.value.skuList[onChangeData.current].specValueNameStr.split(',')
  // console.log('onChangePreviewImg', activeSkuItem.value, selectArr.value)
}

function handleAddressClick() {
  uni.navigateTo({
    url: '/pages/addressPage/index?toSelect=true',
  })
}
</script>

<template>
  <mall-swiper
    :carouses-list="carousesList"
    :safe-area-insets="safeAreaInsets"
    :window-width="windowWidth"
  />
  <view class="bg-white">
    <view class="px-4 pb-4 pt-3 text-2xl font-bold">
      {{ mallData?.goodsName }}
    </view>
    <!--    <view :class="DESCRIPTION_STR[descriptionServiceType].colorClass" class="pl-2">
      <view class="sf-title-round pt-4 pl-3 pb-2">
        <view class="sf-title-text text-2xl font-bold">
          {{ DESCRIPTION_STR[descriptionServiceType].title }}
        </view>
        <view class="text-sm o-color-aid">
          {{ DESCRIPTION_STR[descriptionServiceType].typeStr }}
        </view>
        <view class="text-xs">{{ DESCRIPTION_STR[descriptionServiceType].description }}</view>
      </view>
    </view> -->
  </view>
  <view
    class="relative mt-2 overflow-hidden bg-white p-5 text-xs"
    style="height: 14vw"
    @click="showSelectSheet"
  >
    <view>
      <view class="mt-2 font-bold">
        {{ mallData?.specList[0]?.specName }}
      </view>
      <view class="flex flex-wrap gap-2">
        <view
          v-for="(subItem, subIndex) in mallData?.specList[0]?.specValue"
          :key="subIndex"
          class="f-tag o-border-gray-tag mt-3"
        >
          {{ subItem }}
        </view>
      </view>
    </view>
    <view class="f-list-hidden absolute bottom-0 left-0 z-1 w-full" />
    <view class="absolute right-6 top-6 flex gap-1 text-sm color-gray">
      <view>选择</view>
      <up-icon name="arrow-right" size="14" />
    </view>
  </view>
  <view class="mt-2 bg-white py-4">
    <up-parse :content="mallData?.goodsContent" />
  </view>
  <view class="p-10" />
  <view
    class="f-bottom-box fixed bottom-0 left-0 z-2 box-border w-full flex gap-2 bg-white px-4 pb-3 pt-2"
  >
    <cs-btn-left-button />
    <view
      class="o-bg-primary o-shadow-blue flex flex-grow-1 items-center justify-center rd-2 p-3 text-white font-bold"
      @click="showSelectSheet"
    >
      选择方案
    </view>
  </view>
  <up-action-sheet
    :show="isSheetShow"
    :close-on-click-overlay="true"
    :round="10"
    @close="isSheetShow = false"
  >
    <view
      class="relative px-4 pb-3 pt-4 text-left"
      :style="{ height: `${windowHeight - safeAreaInsets?.top - 160}px` }"
    >
      <up-icon
        class="absolute right-6 top-6 color-gray"
        name="close"
        size="14"
        @click="closeSheetShow"
      />
      <view class="mt-5">
        <view class="flex items-end justify-between gap-4" @click="handleAddressClick">
          <template v-if="addressData.phone">
            <view>
              <view class="flex gap-4">
                <text>{{ addressData.realName }}</text>
                <text>{{ addressData.phone }}</text>
              </view>
              <view class="text-sm">
                {{ addressData.district }}{{ addressData.addressDetail }}
              </view>
            </view>
            <view class="o-color-aid shrink-0 pr-1">
              <up-icon name="arrow-right" size="14" />
            </view>
          </template>
          <view v-else class="text-red-500">
            请添加收货地址
          </view>
        </view>
      </view>
      <view class="o-line mb-2 mt-2" />
      <view class="flex gap-3 px-4 pb-4">
        <up-image
          :width="100"
          :height="100"
          :src="allGoodsImg[imgCurrent]"
          mode="aspectFill"
          @click="toPreviewListImg"
        />
        <view class="flex flex-col justify-between">
          <view>
            <price-box
              :price="parseFloat((activeSkuItem?.originalPrice * quantity).toFixed(2))"
              not-show-decimal
              :size="40"
              class="text-red-500"
            />
            <view v-if="activeSkuItem?.stock === 0" class="mt-2 text-xs color-gray">
              商品暂无库存
            </view>
          </view>
          <up-number-box v-model="quantity" class="mt-6" />
        </view>
      </view>
      <scroll-view class="flex-1" :scroll-y="true">
        <view class="relative mt-3">
          <view v-for="(item, index) in mallData?.specList" :key="item?.specId" class="text-xs">
            <view class="mb-2 font-bold">
              {{ item?.specName }}
            </view>
            <view class="mb-3 flex flex-wrap gap-2">
              <view
                v-for="(subItem, subIndex) in item?.specValue"
                :key="subIndex"
                class="f-tag flex gap-1"
                :class="isSelectTag(index, subItem) ? 'o-border-blue-tag' : 'o-border-gray-tag'"
                @click="handleSelectTag(index, subItem, mallData?.specList?.length)"
              >
                <text>{{ subItem }}</text>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
      <view
        class="flex items-center justify-center gap-2 rd-2 p-3 text-white font-bold"
        :class="loading || activeSkuItem?.stock === 0 ? 'o-bg-primary-disable' : 'o-bg-primary o-shadow-blue'"
        @click="handleSubmit"
      >
        <view v-if="activeSkuItem?.stock === 0">
          暂无库存
        </view>
        <template v-else>
          <view>立即支付</view>
          <price-box
            :price="parseFloat((activeSkuItem?.originalPrice * quantity).toFixed(2))"
            not-show-decimal
            :size="40"
            class="text-white"
          />
        </template>
      </view>
    </view>
  </up-action-sheet>
  <view
    v-if="isShowPreviewImg"
    class="f-img-preview absolute left-0 top-0 flex flex-col items-center justify-center bg-black text-white"
    style="z-index: 99999"
    @click="closePreviewShow"
  >
    <up-icon
      class="absolute right-6 text-white"
      name="close"
      size="16"
      :style="{ top: `${safeAreaInsets?.top + 60}px` }"
    />
    <view class="w-full">
      <up-swiper
        v-model:current="imgCurrent"
        :height="windowWidth"
        :list="allGoodsImg"
        :autoplay="false"
        indicator-style="top:10px;right: 20px"
        @change="onChangePreviewImg"
      />
      <view class="mt-4 p-4 text-center text-sm">
        {{ activeSkuName }}
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.f-bottom-box {
  box-shadow: rgba(0, 0, 0, 0.1) -10vw 0px 10vw;
}

.f-list-hidden {
  height: 13vw;
  background: linear-gradient(rgba(255, 255, 255, 0) 0%, #fff 70%);
}

.f-tag {
  @apply rd-1 px-4 py-1.5;
}

.f-img-preview {
  width: 100vw;
  height: 100vh;
}
</style>
