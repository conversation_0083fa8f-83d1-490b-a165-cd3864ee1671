<script lang="ts" setup>
import { handleCopy } from '@/utils'

const { isSuccess = false } = defineProps<{
  isSuccess?: boolean
}>()
</script>

<template>
  <view class="mb-6 pt-2">
    <view class="mb-2 font-bold">
      整批产品通报步骤：
    </view>
    <view class="flex text-sm">
      <view class="min-w-5 shrink-0 text-primary font-bold">
        1.
      </view>
      <view>用电脑打开 www.gs1helper.com 网站，并点击右上角登录按钮。</view>
    </view>
    <view
      class="flex items-center justify-center gap-1 py-2"
      @click="handleCopy('www.gs1helper.com')"
    >
      <view class="o-barcode-gray-card ml-6 rd-1 px-4 py-1">
        www.gs1helper.com
      </view>
      <up-tag size="mini" plain type="warning" text="点击复制" />
    </view>
    <view class="flex text-sm">
      <view class="min-w-5 shrink-0 text-primary font-bold">
        2.
      </view>
      <view>完成登录后，点击页面上方【信息通报】。</view>
    </view>
    <view class="flex text-sm">
      <view class="min-w-5 shrink-0 text-primary font-bold">
        3.
      </view>
      <view>分别点击【信息通报】-【批量上传】，下载Excel表格，按要求批量填写后，然后【上传】填写好的Excel表格。</view>
    </view>
    <view class="flex text-sm">
      <view class="min-w-5 shrink-0 text-primary font-bold">
        4.
      </view>
      <view>{{ isSuccess ? '完成后请留意客服电话或者添加好友邀请，' : '' }}等待审批结果。</view>
    </view>
  </view>
</template>

<style scoped lang="scss"></style>
