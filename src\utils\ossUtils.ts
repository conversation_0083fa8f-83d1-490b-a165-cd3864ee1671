import CryptoJS from 'crypto-js'

/**
 * OSS Policy 配置接口
 */
export interface OSSPolicyConfig {
  /** 过期时间，单位秒，默认1小时 */
  expiration?: number
  /** 文件大小限制，单位字节，默认10MB */
  maxFileSize?: number
  /** 允许的文件目录前缀 */
  keyPrefix?: string
  /** 成功上传后的状态码 */
  successActionStatus?: string
}

/**
 * OSS 签名结果
 */
export interface OSSSignatureResult {
  policy: string
  signature: string
}

/**
 * 生成阿里云OSS上传Policy
 * @param config Policy配置
 * @returns base64编码的Policy字符串
 */
export function generateOSSPolicy(config: OSSPolicyConfig = {}): string {
  const {
    expiration = 3600, // 默认1小时过期
    maxFileSize = 10 * 1024 * 1024, // 默认10MB
    keyPrefix = '',
    successActionStatus = '200',
  } = config

  // 计算过期时间（当前时间 + expiration秒）
  const expirationDate = new Date(Date.now() + expiration * 1000)
  const expirationISO = expirationDate.toISOString()

  // 构建Policy JSON
  const policyDocument = {
    expiration: expirationISO,
    conditions: [
      // 文件大小限制
      ['content-length-range', 0, maxFileSize],
      // 成功状态码
      ['eq', '$success_action_status', successActionStatus],
    ],
  }

  // 如果指定了目录前缀，添加到条件中
  if (keyPrefix) {
    policyDocument.conditions.push(['starts-with', '$key', keyPrefix])
  }

  // 转换为JSON字符串并进行base64编码
  const policyString = JSON.stringify(policyDocument)
  const policyBase64 = CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(policyString))

  return policyBase64
}

/**
 * 生成阿里云OSS签名
 * @param policy base64编码的Policy字符串
 * @param accessKeySecret 阿里云AccessKeySecret
 * @returns HMAC-SHA1签名的base64编码
 */
export function generateOSSSignature(policy: string, accessKeySecret: string): string {
  // 使用HMAC-SHA1算法对Policy进行签名
  const signature = CryptoJS.HmacSHA1(policy, accessKeySecret)
  // 转换为base64编码
  const signatureBase64 = CryptoJS.enc.Base64.stringify(signature)

  return signatureBase64
}

/**
 * 生成完整的OSS签名信息
 * @param accessKeySecret 阿里云AccessKeySecret
 * @param config Policy配置
 * @returns 包含policy和signature的对象
 */
export function generateOSSSignatureInfo(
  accessKeySecret: string,
  config: OSSPolicyConfig = {},
): OSSSignatureResult {
  // 生成Policy
  const policy = generateOSSPolicy(config)

  // 生成签名
  const signature = generateOSSSignature(policy, accessKeySecret)

  return {
    policy,
    signature,
  }
}

/**
 * 验证OSS配置是否完整
 * @param ossConfig OSS配置对象
 * @returns 是否配置完整
 */
export function validateOSSConfig(ossConfig: {
  url?: string
  accessKeyId?: string
  accessKeySecret?: string
  securityToken?: string
  policy?: string
  signature?: string
}): boolean {
  const requiredFields = [
    'url',
    'accessKeyId',
    'accessKeySecret',
    'securityToken',
    'policy',
    'signature',
  ]

  return requiredFields.every((field) => {
    const value = ossConfig[field as keyof typeof ossConfig]
    return value && value.trim() !== ''
  })
}

/**
 * 生成随机文件名
 * @param originalName 原始文件名
 * @param prefix 文件名前缀
 * @returns 新的文件名
 */
export function generateRandomFileName(originalName: string, prefix: string = ''): string {
  const timestamp = Date.now()
  const randomStr = Math.random().toString(36).substring(2, 8)
  const extension = originalName.substring(originalName.lastIndexOf('.'))

  return `${prefix}${timestamp}_${randomStr}${extension}`
}
