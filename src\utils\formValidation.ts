/**
 * 表单验证工具函数
 */

/**
 * 显示错误提示
 * @param message 错误提示信息
 * @param duration 提示持续时间，默认2000ms
 */
export function showError(message: string, duration = 2000) {
  uni.showToast({
    title: message,
    icon: 'none',
    duration,
  })
}

/**
 * 滚动到指定元素
 * @param elementId 要滚动到的元素ID
 * @param duration 滚动动画持续时间，默认300ms
 */
export function scrollToElement(elementId: string, duration = 300) {
  // 添加延时确保DOM已渲染和Toast显示
  setTimeout(() => {
    console.log(`Attempting to scroll to element: ${elementId}`)

    const query = uni.createSelectorQuery()

    // 获取元素位置
    query.select(`#${elementId}`).boundingClientRect((elementRect) => {
      console.log(`Element rect for ${elementId}:`, elementRect)

      if (elementRect && !Array.isArray(elementRect) && elementRect.top !== undefined) {
        // 获取页面滚动信息
        const scrollQuery = uni.createSelectorQuery()
        scrollQuery.selectViewport().scrollOffset((scrollInfo) => {
          console.log(`Scroll info:`, scrollInfo)

          let targetScrollTop = 0

          if (scrollInfo && !Array.isArray(scrollInfo) && scrollInfo.scrollTop !== undefined) {
            // 计算目标滚动位置：元素顶部位置 + 当前滚动位置 - 偏移量
            targetScrollTop = elementRect.top + scrollInfo.scrollTop - 80
          }
          else {
            // 如果无法获取滚动位置，直接使用元素位置
            targetScrollTop = elementRect.top - 80
          }

          // 确保滚动位置不为负数
          targetScrollTop = Math.max(0, targetScrollTop)

          console.log(`Scrolling to element ${elementId}, target position: ${targetScrollTop}`)

          uni.pageScrollTo({
            scrollTop: targetScrollTop,
            duration,
            success: () => {
              console.log(`Successfully scrolled to ${elementId}`)
            },
            fail: (err) => {
              console.error(`Failed to scroll to ${elementId}:`, err)
            },
          })
        }).exec()
      }
      else {
        console.warn(`Element with id "${elementId}" not found`)
      }
    }).exec()
  }, 300) // 增加延时确保DOM完全渲染
}

/**
 * 显示错误提示并滚动到指定元素
 * @param message 错误提示信息
 * @param elementId 要滚动到的元素ID
 * @param duration 滚动动画持续时间，默认300ms
 */
export function showErrorAndScrollTo(message: string, elementId: string, duration = 300) {
  showError(message)
  scrollToElement(elementId, duration)
}

/**
 * 表单验证规则接口
 */
export interface ValidationRule {
  /** 验证条件 */
  condition: boolean
  /** 错误提示信息 */
  message: string
  /** 要滚动到的元素ID（可选） */
  elementId?: string
  /** 错误回调函数（可选） */
  onError?: () => void
}

/**
 * 批量验证表单字段
 * @param rules 验证规则数组
 * @returns 是否验证通过
 */
export function validateForm(rules: ValidationRule[]): boolean {
  for (const rule of rules) {
    if (rule.condition) {
      if (rule.elementId) {
        showErrorAndScrollTo(rule.message, rule.elementId)
      }
      else {
        showError(rule.message)
      }

      // 执行错误回调
      if (rule.onError) {
        rule.onError()
      }

      return false
    }
  }
  return true
}
