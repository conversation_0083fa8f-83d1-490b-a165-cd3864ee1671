import dayjs from 'dayjs'
import { defineStore } from 'pinia'
import { reactive, ref } from 'vue'
import { UPLOAD_IMG_MAXSIZE } from '@/enums'
import { getStsTokenApi, InfoReportGoodsImageUploadPath } from '@/service/stsTokenApi'
import { generateOSSSignatureInfo, validateOSSConfig } from '@/utils/ossUtils'

// 存储键名
const OSS_CONFIG_STORAGE_KEY = 'oss_config_storage'
const OSS_EXPIRE_TIME_STORAGE_KEY = 'oss_expire_time_storage'
const SERVER_EXPIRE_TIME_STORAGE_KEY = 'server_expire_time_storage'

// OSS配置接口
interface OSSConfig {
  url: string
  accessKeyId: string
  accessKeySecret: string
  securityToken: string
  policy: string
  signature: string
}

export const ossTokenStore = defineStore('ossTokenStore', () => {
  // 安全地从本地存储恢复配置
  let savedConfig: Partial<OSSConfig> = {}
  let savedOssExpireTime = 0
  let savedServerExpireTime = 0

  try {
    const configData = uni.getStorageSync(OSS_CONFIG_STORAGE_KEY)
    savedConfig = (configData && typeof configData === 'object') ? configData : {}

    const ossExpireData = uni.getStorageSync(OSS_EXPIRE_TIME_STORAGE_KEY)
    savedOssExpireTime = Number(ossExpireData) || 0

    const serverExpireData = uni.getStorageSync(SERVER_EXPIRE_TIME_STORAGE_KEY)
    savedServerExpireTime = Number(serverExpireData) || 0
  }
  catch (error) {
    console.error('读取本地存储的OSS配置失败:', error)
    savedConfig = {}
    savedOssExpireTime = 0
    savedServerExpireTime = 0
  }

  const ossConfig = reactive<OSSConfig>({
    url: savedConfig.url || '',
    accessKeyId: savedConfig.accessKeyId || '',
    accessKeySecret: savedConfig.accessKeySecret || '',
    securityToken: savedConfig.securityToken || '',
    policy: savedConfig.policy || '',
    signature: savedConfig.signature || '',
  })

  // 有效时间一小时 (毫秒)
  const TOKEN_EXPIRE_TIME = 50 * 60 * 1000
  const ossEffectiveTime = ref(savedOssExpireTime) // 本地计算的过期时间
  const serverExpirationTime = ref(savedServerExpireTime) // 服务器返回的过期时间
  const isLoading = ref(false)

  // 保存配置到本地存储
  function saveToStorage() {
    try {
      uni.setStorageSync(OSS_CONFIG_STORAGE_KEY, {
        url: ossConfig.url,
        accessKeyId: ossConfig.accessKeyId,
        accessKeySecret: ossConfig.accessKeySecret,
        securityToken: ossConfig.securityToken,
        policy: ossConfig.policy,
        signature: ossConfig.signature,
      })
      uni.setStorageSync(OSS_EXPIRE_TIME_STORAGE_KEY, ossEffectiveTime.value)
      uni.setStorageSync(SERVER_EXPIRE_TIME_STORAGE_KEY, serverExpirationTime.value)
      console.log('OSS配置已保存到本地存储')
    }
    catch (error) {
      console.error('保存OSS配置到本地存储失败:', error)
    }
  }

  // 从本地存储清除配置
  function clearStorage() {
    try {
      uni.removeStorageSync(OSS_CONFIG_STORAGE_KEY)
      uni.removeStorageSync(OSS_EXPIRE_TIME_STORAGE_KEY)
      uni.removeStorageSync(SERVER_EXPIRE_TIME_STORAGE_KEY)
      console.log('已清除本地存储的OSS配置')
    }
    catch (error) {
      console.error('清除本地存储的OSS配置失败:', error)
    }
  }

  // 检查token是否过期 - 任意一个过期都需要重新获取
  function isTokenExpired() {
    // 如果时间戳为0，说明没有有效的token
    if (!ossEffectiveTime.value || !serverExpirationTime.value) {
      return true
    }

    const now = Date.now()
    const localExpired = now > ossEffectiveTime.value
    const serverExpired = now > serverExpirationTime.value

    if (localExpired || serverExpired) {
      console.log('Token过期检查:', {
        localExpired,
        serverExpired,
        localExpireTime: new Date(ossEffectiveTime.value),
        serverExpireTime: new Date(serverExpirationTime.value),
        currentTime: new Date(now),
      })
      return true
    }

    return false
  }

  // 获取OSS临时访问凭证
  async function getOssToken() {
    // 检查token是否过期和配置是否完整
    const expired = isTokenExpired()
    const configValid = validateOSSConfig(ossConfig)

    // 如果token未过期且配置完整，直接返回
    if (!expired && configValid) {
      console.log('使用缓存的OSS配置')
      return true
    }

    // 如果过期或配置无效，清除配置
    if (expired || !configValid) {
      console.log('Token已过期或配置无效，清除配置')
      clearToken()
    }

    // 防止重复请求
    if (isLoading.value) {
      console.log('正在获取OSS配置，请稍候...')
      // 等待当前请求完成
      while (isLoading.value) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }
      // 重新检查是否已经获取到有效配置
      return !isTokenExpired() && validateOSSConfig(ossConfig)
    }

    isLoading.value = true

    try {
      const res = await getStsTokenApi()
      console.log('获取STS凭证响应:', res)

      if (res.success) {
        ossConfig.accessKeyId = res.data.accessKeyId
        ossConfig.accessKeySecret = res.data.accessKeySecret
        ossConfig.securityToken = res.data.securityToken
        // 实际项目中，OSS的URL应该从环境变量或后端获取
        ossConfig.url = import.meta.env.VITE_OSS_UPLOAD_URL || ''
        console.log('OSS配置URL:', ossConfig.url)

        // 解析服务器返回的过期时间
        const serverExpiration = dayjs(res.data.expiration).valueOf()
        serverExpirationTime.value = serverExpiration

        // 根据STS凭证生成policy和signature
        try {
          const signatureInfo = generateOSSSignatureInfo(res.data.accessKeySecret, {
            expiration: 3600, // 1小时过期
            maxFileSize: UPLOAD_IMG_MAXSIZE, // 使用项目中定义的最大文件大小
            keyPrefix: InfoReportGoodsImageUploadPath, // 使用图片上传路径作为前缀
            successActionStatus: '200',
          })

          ossConfig.policy = signatureInfo.policy
          ossConfig.signature = signatureInfo.signature

          console.log('OSS签名生成成功:', {
            policy: `${ossConfig.policy.substring(0, 50)}...`,
            signature: `${ossConfig.signature.substring(0, 20)}...`,
          })

          // 验证OSS配置是否完整
          if (!validateOSSConfig(ossConfig)) {
            throw new Error('OSS配置不完整')
          }

          // 设置过期时间
          const localExpiration = Date.now() + TOKEN_EXPIRE_TIME
          ossEffectiveTime.value = localExpiration

          // 使用较早的过期时间作为实际有效期
          const effectiveExpiration = Math.min(localExpiration, serverExpiration)

          console.log('OSS配置验证通过，过期时间设置:', {
            localExpiration: new Date(localExpiration),
            serverExpiration: new Date(serverExpiration),
            effectiveExpiration: new Date(effectiveExpiration),
            remainingTime: `${Math.round((effectiveExpiration - Date.now()) / 1000 / 60)}分钟`,
          })

          // 保存到本地存储
          saveToStorage()

          return true
        }
        catch (signError) {
          console.error('生成OSS签名失败:', signError)
          // 清除可能的无效配置
          clearToken()
          const errorMsg = signError instanceof Error ? signError.message : '生成上传签名失败'
          uni.showToast({
            title: errorMsg,
            icon: 'none',
            duration: 2000,
          })
          return false
        }
      }
      else {
        const errorMsg = res.errMessage || '获取上传凭证失败'
        console.error('获取STS凭证失败:', errorMsg)
        // 清除可能的无效配置
        clearToken()
        uni.showToast({
          title: errorMsg,
          icon: 'none',
          duration: 2000,
        })
        return false
      }
    }
    catch (error) {
      const errorMsg = error instanceof Error ? error.message : '网络请求失败'
      console.error('获取STS凭证异常:', error)
      // 清除可能的无效配置
      clearToken()
      uni.showToast({
        title: `获取上传凭证失败: ${errorMsg}`,
        icon: 'none',
        duration: 2000,
      })
      return false
    }
    finally {
      isLoading.value = false
    }
  }

  // 清除token
  function clearToken() {
    Object.assign(ossConfig, {
      url: '',
      accessKeyId: '',
      accessKeySecret: '',
      securityToken: '',
      policy: '',
      signature: '',
    })
    ossEffectiveTime.value = 0
    serverExpirationTime.value = 0
    // 清除本地存储
    clearStorage()
  }

  // 初始化时检查本地存储的token是否有效
  function initializeFromStorage() {
    // 检查是否有保存的配置
    if (!savedOssExpireTime || !savedServerExpireTime) {
      console.log('本地存储中没有完整的OSS配置时间信息')
      return
    }

    const now = Date.now()
    const localExpired = now > savedOssExpireTime
    const serverExpired = now > savedServerExpireTime
    const configValid = validateOSSConfig(ossConfig)

    console.log('从本地存储恢复OSS配置:', {
      localExpireTime: new Date(savedOssExpireTime),
      serverExpireTime: new Date(savedServerExpireTime),
      currentTime: new Date(now),
      localExpired,
      serverExpired,
      configValid,
      remainingTime: configValid && !localExpired && !serverExpired
        ? `${Math.round((Math.min(savedOssExpireTime, savedServerExpireTime) - now) / 1000 / 60)}分钟`
        : '已过期',
    })

    // 检查是否需要清除配置
    if (localExpired || serverExpired || !configValid) {
      const reason = !configValid ? '配置不完整' : '已过期'
      console.log(`本地存储的OSS配置${reason}，清除配置`)
      clearToken()
    }
    else {
      console.log('成功从本地存储恢复有效的OSS配置')
    }
  }

  // 初始化
  initializeFromStorage()

  return {
    ossConfig,
    ossEffectiveTime,
    serverExpirationTime,
    isLoading,
    getOssToken,
    clearToken,
    isTokenExpired,
    saveToStorage,
    clearStorage,
  }
})
