<script lang="ts" setup>
import type { ServerType } from '@/enums'
import DiscountBox from '@/components/Price/DiscountBox.vue'
import { couponTypeStr } from '@/enums'

const props = withDefaults(
  defineProps<{
    data: any
    disabled?: boolean
  }>(),
  {
    disabled: false,
  },
)
const format = 'DD 天 HH 时 mm 分 ss 秒'
const expired = ref(false) // 优惠券是否过期

function handleFinish() {
  expired.value = true
}

function calculateExpirationTime(generationTime: string, expirationDays: number): string {
  // 将输入的时间字符串从 "YYYY-MM-DD HH:mm:ss" 转换为 "YYYY-MM-DDTHH:mm:ss"
  const isoFormattedTime = generationTime.replace(' ', 'T')

  // 创建 Date 对象
  const genDate = new Date(isoFormattedTime)

  // 检查是否成功创建了 Date 对象
  if (Number.isNaN(genDate.getTime())) {
    throw new TypeError('Invalid date format. Please use "YYYY-MM-DD HH:mm:ss" format.')
  }

  // 计算到期时间
  const expirationDate = new Date(genDate.getTime() + expirationDays * 24 * 60 * 60 * 1000)

  // 格式化输出到期时间（不包括秒）
  const year = expirationDate.getFullYear()
  const month = String(expirationDate.getMonth() + 1).padStart(2, '0')
  const day = String(expirationDate.getDate()).padStart(2, '0')
  const hours = String(expirationDate.getHours()).padStart(2, '0')
  const minutes = String(expirationDate.getMinutes()).padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}`
}

function getServerTypeStr(serverType: ServerType) {
  switch (serverType) {
    case 1:
      return '条码制作专用'
    case 2:
      return '产品通报专用'
    default:
      return '通用'
  }
}
</script>

<template>
  <view :class="disabled ? 'f-expired' : ''">
    <view class="flex justify-between">
      <view>
        <view>{{ data.couponName }}</view>
        <view>
          <view class="f-tag o-tag o-bg-primary-light mt-1 rd-1 text-primary">
            {{ getServerTypeStr(data.serverType) }}
          </view>
        </view>
        <view class="mt-2 text-sm">
          <view v-if="expired || data.isTimeOut">
            <view>
              有效期
              <text>{{ data.expiryDay }}天</text>
            </view>
            <view>
              已于
              <text>{{ calculateExpirationTime(data.grantDate, data.expiryDay) }}</text>
              过期
            </view>
          </view>
          <view v-if="!(expired || data.isTimeOut)" class="flex items-center gap-1">
            <view class="o-color-aid">
              仅剩
            </view>
            <up-count-down
              :format="format"
              :time="data.expirySeconds * 1000"
              @finish="handleFinish"
            />
          </view>
          <view v-if="data.minUsagePrice" class="mt-2">
            最低使用金额
            <text>￥{{ data.minUsagePrice }}</text>
          </view>
          <view v-if="data.vendorCode">
            适用厂商识别码：{{ data.vendorCode }}
          </view>
        </view>
      </view>
      <view class="flex flex-col items-end">
        <view class="flex items-baseline gap-1 text-red-500">
          <template v-if="data.couponType === 1">
            <view class="text-sm">
              ￥
            </view>
            <view class="text-2xl font-bold">
              {{ data.couponPrice }}
            </view>
          </template>
          <discount-box v-if="data.couponType === 2" :discount="data.discount" :size="48" />
        </view>
        <view class="o-color-aid text-sm">
          {{ couponTypeStr(data.couponType) }}
        </view>
      </view>
    </view>
    <view class="o-line mb-1 mt-2" />
    <view class="o-color-aid text-sm">
      {{ data.description }}
    </view>
    <view v-if="expired || data.isTimeOut" class="f-time-out-img absolute" />
  </view>
</template>

<style lang="scss" scoped>
.f-tag {
  width: fit-content;
}

.f-expired {
  opacity: 0.4;
}

.f-time-out-img {
  $w: 120rpx;

  width: $w;
  height: $w;
  background-image: url('https://wx.gs1helper.com/images/isTimeOut.png');
  background-size: 100% 100%;
  right: -20rpx;
  bottom: -10rpx;
  transform: rotate(-30deg);
}
</style>
