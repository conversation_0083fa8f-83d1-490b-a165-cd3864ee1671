<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "条码制作"
  }
}
</route>

<script lang="ts" setup>
import type {
  CouponPageResData,
  CreateOrderV3Params,
  PriceTempListResData,
} from '@/service/orderApi'
import debounce from 'debounce'
import { storeToRefs } from 'pinia'
import CsLongButton from '@/components/customer/csLongButton.vue'
import { BlowUpStr, DESCRIPTION_STR } from '@/components/descriptionStr'
import ServerTitle from '@/components/serverTitle.vue'
import { BarType, CouponType, ServerType } from '@/enums'
import { Color } from '@/enums/colorEnum'
import { OrderDirection } from '@/enums/httpEnum'
import {
  couponPageApi,
  createOrderV3Api,
  priceTempListApi,
} from '@/service/orderApi'
import { useCouponStore } from '@/store/couponStore'
import { orderStore } from '@/store/orderStore'
import { useServiceStore } from '@/store/serviceStore'
import { useUserStore } from '@/store/user'
import { vendorCodeStore } from '@/store/vendorCodeStore'
import { validateForm } from '@/utils/formValidation'

/* defineOptions({
  name: 'MakeFilm',
}) */

enum AdvancedService {
  onlyBarCode = '仅条码生成',
  withReport = '标准条码服务',
}

type ErrorTypeColor = typeof Color.inputColor | typeof Color.red

const serviceStore = useServiceStore()
const { descriptionServiceType, isHasOtherServer, barCodePrice, extraPrice, isFirstCode }
  = storeToRefs(serviceStore)
const useVendorCodeStore = vendorCodeStore()
const { companyName: searchCompanyName, vendorCode: searchVendorCode }
  = storeToRefs(useVendorCodeStore)
const useOrderStore = orderStore()
const { vendorCode: storeVendorCode, orderCode, serverType } = storeToRefs(useOrderStore)
const userStore = useUserStore()
const { userId } = storeToRefs(userStore)
const { couponList, bestDiscountItem } = storeToRefs(useCouponStore)

const MAX_SIZE = 500 // 每次允许最大生成数
// 缓存优惠券数据，避免重复请求
const couponCache = new Map<boolean, { data: CouponPageResData[], bestDiscount: CouponPageResData | null }>()
const isShowSelectSize = ref(false)
const loading = ref(false)
const isShowPackingCodePick = ref(false)
const packingCode = ref(1) // 包装指示符
const coefficient = ref('') // 放大系数
const vendorCode = ref<string>('') // 厂商识别代码
const packingCodeArr = ref([[1, 2, 3, 4, 5, 6, 7, 8, 9]])
const placeholderFrom = ref('例 000')
const placeholderTo = ref('例 200')
const advancedServiceCurrent = ref(0)
const advancedServiceList = [AdvancedService.withReport, AdvancedService.onlyBarCode]
const serveData = ref<PriceTempListResData[]>([])
const discount = ref(1)
const priceStr = ref('')
const isShowStep = ref(false)
const maxlength = reactive({
  vendorCode: 9,
})
const size = reactive({
  width: '',
  height: '',
})
const projectCode = reactive({
  from: '',
  to: '',
})
const errorTypeColor = reactive<{
  vendorCode: ErrorTypeColor
  from: ErrorTypeColor
  to: ErrorTypeColor
  packingCode: ErrorTypeColor
}>({
  vendorCode: Color.inputColor,
  from: Color.inputColor,
  to: Color.inputColor,
  packingCode: Color.inputColor,
})

const advancedService = ref<AdvancedService>(AdvancedService.withReport)

function handleSelect(v: any) {
  // console.log(value)
  size.width = `${v.w} mm`
  size.height = `${v.h} mm`
  coefficient.value = v.coefficient
}

// 防止重复提交
const handleSubmit = debounce(
  () => {
    // 基础验证规则
    const basicValidationRules = [
      {
        condition: descriptionServiceType.value === BarType.ITF14 && (packingCode.value < 1 || packingCode.value > 9 || Number.isNaN(packingCode.value)),
        message: '包装指示符应为1位1~9数字',
        onError: () => {
          errorTypeColor.packingCode = Color.red
        },
      },
      {
        condition: vendorCode.value.length < 7 || vendorCode.value.length > 9,
        message: '厂商识别代码为7~9位数',
        onError: () => {
          errorTypeColor.vendorCode = Color.red
        },
      },
    ]

    if (!validateForm(basicValidationRules)) {
      return
    }
    autocomplete('from')
    autocomplete('to')
    // vendorCode.value前三位数为690~697之间的数
    const companyCode = Number(vendorCode.value.substring(0, 3))
    if (companyCode === 697) {
      if (vendorCode.value.length !== 9) {
        uni.showToast({
          icon: 'none',
          title: '697开头厂商识别代码为9位数',
          duration: 2000,
        })
        errorTypeColor.vendorCode = Color.red
        return
      }
      if (projectCode.from.length !== 3) {
        uni.showToast({
          icon: 'none',
          title: '项目代码应为3位数',
          duration: 2000,
        })
        errorTypeColor.from = Color.red
        return
      }
    }
    else if (companyCode >= 692 && companyCode <= 696) {
      if (vendorCode.value.length !== 8) {
        uni.showToast({
          icon: 'none',
          title: '690~696开头厂商识别代码为8位数',
          duration: 2000,
        })
        errorTypeColor.vendorCode = Color.red
        return
      }
      if (projectCode.from.length !== 4) {
        uni.showToast({
          icon: 'none',
          title: '项目代码应为4位数',
          duration: 2000,
        })
        errorTypeColor.from = Color.red
        return
      }
    }
    else if (companyCode === 690 || companyCode === 691) {
      if (vendorCode.value.length !== 7) {
        uni.showToast({
          icon: 'none',
          title: '690~691开头厂商识别代码为7位数',
          duration: 2000,
        })
        errorTypeColor.vendorCode = Color.red
        return
      }
      if (projectCode.from.length !== 5) {
        uni.showToast({
          icon: 'none',
          title: '项目代码应为5位数',
          duration: 2000,
        })
        errorTypeColor.from = Color.red
        return
      }
    }
    else {
      uni.showToast({
        icon: 'none',
        title: '厂商识别代码前三位数为690~697之间的数',
        duration: 2000,
      })
      errorTypeColor.vendorCode = Color.red
      return
    }
    if (projectCode.from.length < 3 || projectCode.from.length > 5) {
      uni.showToast({
        icon: 'none',
        title: '项目代码为3~5位数',
        duration: 2000,
      })
      errorTypeColor.from = Color.red
      return
    }
    else {
      if (projectCode.to.length > 0) {
        if (projectCode.to.length !== projectCode.from.length) {
          uni.showToast({
            icon: 'none',
            title: '项目代码位数前后要相同',
            duration: 2000,
          })
          errorTypeColor.to = Color.red
          return
        }
        else {
          if (Number(projectCode.to) < Number(projectCode.from)) {
            uni.showToast({
              icon: 'none',
              title: '项目代码需要从小到大',
              duration: 2000,
            })
            errorTypeColor.to = Color.red
            return
          }
        }
      }
    }
    const codeLength = Number(projectCode.to) - Number(projectCode.from) + 1
    if (codeLength > MAX_SIZE) {
      uni.showToast({
        icon: 'none',
        title: `单次最多生成${MAX_SIZE}条条码，请重新输入项目代码`,
        duration: 2000,
      })
      errorTypeColor.to = Color.red
      return
    }
    if (coefficient.value === '') {
      uni.showToast({
        icon: 'none',
        title: '请选择放大系数',
        duration: 2000,
      })
      return
    }
    let startProjectCode = projectCode.to
    if (projectCode.to === '') {
      startProjectCode = projectCode.from
    }
    // 用于传递到下一页
    // isHasOtherServer.value = advancedService.value === AdvancedService.withReport
    const params: CreateOrderV3Params = {
      isHasOtherServer: isHasOtherServer.value,
      serverType: ServerType.makeFilm,
      barType: descriptionServiceType.value as BarType,
      startProjectCode: projectCode.from,
      endProjectCode: startProjectCode,
      size: Number(coefficient.value),
      userId: userId.value,
      vendorCode: vendorCode.value,
    }
    params.fromTo = import.meta.env.VITE_FROM_PLATFORM

    if (descriptionServiceType.value === BarType.ITF14) {
      params.packageCode = Number(packingCode.value)
    }
    searchVendorCode.value = vendorCode.value // 记忆厂商码，下次不用再填
    loading.value = true
    uni.showLoading({ title: '生成预览中...' })
    createOrderV3Api(params)
      .then((res: any) => {
        const d = res.data
        // 厂商识别代码，用于支付时筛选优惠券
        storeVendorCode.value = vendorCode.value
        orderCode.value = d.orderCode
        serverType.value = ServerType.makeFilm
        barCodePrice.value = d.barCodePrice
        extraPrice.value = d.extraPrice
        isFirstCode.value = d.isFirstCode
        loading.value = false
        uni.hideLoading()
        uni.navigateTo({
          url: '/pages/orderConfirm/makeFilm',
        })
      })
      .catch(() => {
        loading.value = false
        uni.hideLoading()
      })
  },
  1000,
  { immediate: true },
)

function autocomplete(where: 'from' | 'to') {
  const companyCode = Number(vendorCode.value.substring(0, 3))
  const length = projectCode[where].length
  if (companyCode === 697) {
    // 项目代码应为3位数
    switch (length) {
      case 1:
        projectCode[where] = `00${projectCode[where]}`
        break
      case 2:
        projectCode[where] = `0${projectCode[where]}`
        break
    }
  }
  else if (companyCode >= 692 && companyCode <= 696) {
    // 项目代码应为4位数
    switch (length) {
      case 1:
        projectCode[where] = `000${projectCode[where]}`
        break
      case 2:
        projectCode[where] = `00${projectCode[where]}`
        break
      case 3:
        projectCode[where] = `0${projectCode[where]}`
        break
    }
  }
  else if (companyCode === 690 || companyCode === 691) {
    // 项目代码应为5位数
    switch (length) {
      case 1:
        projectCode[where] = `0000${projectCode[where]}`
        break
      case 2:
        projectCode[where] = `000${projectCode[where]}`
        break
      case 3:
        projectCode[where] = `00${projectCode[where]}`
        break
      case 4:
        projectCode[where] = `0${projectCode[where]}`
        break
    }
  }
}

function handleProjectCodeBlur(where: 'from' | 'to') {
  autocomplete(where)
}

function handleInputManufacturerIdCode() {
  const companyCode = Number(vendorCode.value.substring(0, 3))
  if (companyCode === 697) {
    maxlength.vendorCode = 9
    placeholderFrom.value = '例 000'
    placeholderTo.value = '例 200'
  }
  else if (companyCode >= 692 && companyCode <= 696) {
    maxlength.vendorCode = 8
    placeholderFrom.value = '例 0000'
    placeholderTo.value = '例 0200'
  }
  else if (companyCode === 690 || companyCode === 691) {
    maxlength.vendorCode = 7
    placeholderFrom.value = '例 00000'
    placeholderTo.value = '例 00200'
  }
}

function scrollToElement() {
  isShowSelectSize.value = !isShowSelectSize.value
  /*  if(isShowSelectSize.value) {
    uni.pageScrollTo({
      selector: '#targetElement',
    })
  } */
}

function toGetVendorCode() {
  uni.navigateTo({
    url: '/pages/makeFilm/vendorCodePage',
  })
}

function handleVendorCodeClick() {
  errorTypeColor.vendorCode = Color.inputColor
  // 用于选了别人公司、改正错误后，需要清空已选的厂商识别码
  searchCompanyName.value = ''
  searchVendorCode.value = ''
}

const handleInput = debounce(() => {
  const str = '，预估每个 ￥'
  if (projectCode.to === '') {
    priceStr.value = str + getDiscountPrice(serveData.value.find(item => item.endNum <= 9).price)
    return
  }
  const from = Number(projectCode.from) || 0
  const to = Number(projectCode.to) || 0
  const differenceValue = to - from + 1
  if (differenceValue <= 0)
    return
  if (differenceValue <= 9) {
    priceStr.value = str + getDiscountPrice(serveData.value.find(item => item.endNum <= 9).price)
  }
  else if (differenceValue <= 49) {
    priceStr.value
      = str
        + getDiscountPrice(serveData.value.find(item => item.startNum > 9 && item.endNum <= 49).price)
  }
  else if (differenceValue <= 99) {
    priceStr.value
      = str
        + getDiscountPrice(
          serveData.value.find(item => item.startNum > 49 && item.endNum <= 99).price,
        )
  }
  else if (differenceValue <= 199) {
    priceStr.value
      = str
        + getDiscountPrice(
          serveData.value.find(item => item.startNum > 99 && item.endNum <= 199).price,
        )
  }
  else {
    priceStr.value
      = str + getDiscountPrice(serveData.value.find(item => item.startNum > 199).price)
  }
}, 250)

function handleEasyInput(num: number) {
  errorTypeColor.from = Color.inputColor
  errorTypeColor.to = Color.inputColor
  switch (num) {
    case 1:
      projectCode.from = '000'
      projectCode.to = '000'
      break
    case 10:
      projectCode.from = '000'
      projectCode.to = '009'
      break
    case 50:
      projectCode.from = '000'
      projectCode.to = '049'
      break
    case 100:
      projectCode.from = '000'
      projectCode.to = '099'
      break
    case 200:
      projectCode.from = '000'
      projectCode.to = '199'
      break
    case 500:
      projectCode.from = '000'
      projectCode.to = '499'
      break
  }
  autocomplete('from')
  autocomplete('to')
  handleInput()
}

function handleAdvancedService(index: number) {
  advancedServiceCurrent.value = index
  advancedService.value = advancedServiceList[index]
  isHasOtherServer.value = advancedService.value === AdvancedService.withReport
  console.log('advancedService.value:', advancedService.value)
  // TODO 重新计算折扣
  getCouponList().then(() => {
    console.log('🚀 ~ handleAdvancedService ~ projectCode:', projectCode.from)
    if (projectCode.from !== '' || projectCode.to !== '') {
      handleInput()
    }
  })
}

function packingCodeConfirm(e: any) {
  packingCode.value = e.value[0]
  isShowPackingCodePick.value = false
}

/**
 * 找出折扣券中折扣最小的
 * @param list
 */
function findBestDiscountCoupon(list: CouponPageResData[]) {
  // 过滤出符合条件的对象
  const filteredList = list.filter(
    item => item.couponType === CouponType.discount && item.discount,
  )
  // 如果过滤后的列表为空，则返回null
  if (filteredList.length === 0) {
    return null
  }
  return filteredList.reduce(
    (max, current) => (current.discount < max.discount ? current : max),
    filteredList[0],
  )
}

// 获取优惠券列表
function getCouponList() {
  return new Promise((resolve, reject) => {
    const cacheKey = isHasOtherServer.value

    // 检查缓存
    if (couponCache.has(cacheKey)) {
      const cached = couponCache.get(cacheKey)!
      couponList.value = cached.data
      bestDiscountItem.value = cached.bestDiscount
      if (bestDiscountItem.value) {
        discount.value = bestDiscountItem.value?.discount || 1
      }
      resolve(true)
      return
    }

    // 缓存中没有数据，发起请求
    couponPageApi({
      isStandardCode: isHasOtherServer.value,
      isUsed: 0,
      isTimeOut: 0,
      serverType: serverType.value,
      groupBy: '',
      needTotalCount: true,
      orderBy: '',
      orderDirection: OrderDirection.def,
      pageIndex: 1,
      pageSize: 10000,
      userId: userId.value,
    }).then((res) => {
      const bestDiscount = findBestDiscountCoupon(res.data)

      // 缓存结果
      couponCache.set(cacheKey, {
        data: res.data,
        bestDiscount,
      })

      couponList.value = res.data
      bestDiscountItem.value = bestDiscount
      if (bestDiscountItem.value) {
        // 确保判断对象正确
        discount.value = bestDiscountItem.value?.discount || 1
      }
      resolve(true)
    }).catch((err) => {
      reject(err)
    })
  })
}

function getTempList() {
  return priceTempListApi({
    tempType: serverType.value,
    isDefault: 0,
  }).then((res) => {
    serveData.value = res.data
    return res
  })
}

function getDiscountPrice(price: number) {
  const discountedPrice = price * discount.value
  return Number.parseFloat(discountedPrice.toFixed(2)).toString()
}

onMounted(() => {
  userStore.login()
  console.log('onMounted', descriptionServiceType.value)
  loading.value = true
  Promise.all([getCouponList(), getTempList()]).then(() => {
    loading.value = false
  })
  if (descriptionServiceType.value === BarType.ITF14) {
    uni.setNavigationBarTitle({
      title: '箱码制作',
    })
  }
  nextTick(() => {
    if (descriptionServiceType.value === BarType.ITF14) {
      coefficient.value = '0.60'
    }
    else {
      coefficient.value = '1.00'
    }
  })
})

onShow(() => {
  console.log('onShow', descriptionServiceType.value)
  if (searchVendorCode.value) {
    vendorCode.value = searchVendorCode.value
    handleInputManufacturerIdCode()
  }
})
</script>

<template>
  <view class="bg-white">
    <ServerTitle />
    <view class="flex items-center justify-between pr-3">
      <view class="py-5 pl-5 pr-1">
        <view class="font-bold">
          编码组成：
        </view>
        <view class="mt-1 pl-1 text-sm">
          <view
            v-for="(item, index) in DESCRIPTION_STR[descriptionServiceType].codedComposition" :key="index"
            class="flex gap-2"
          >
            <view class="o-dot" />
            <view>{{ item }}</view>
          </view>
        </view>
      </view>
      <up-image
        :height="DESCRIPTION_STR[descriptionServiceType].img.height"
        :src="DESCRIPTION_STR[descriptionServiceType].img.data"
        :width="DESCRIPTION_STR[descriptionServiceType].img.width" class="shrink-0"
      />
    </view>
  </view>
  <view class="mt-3 px-4">
    <view class="flex flex-col items-center justify-center rd-2 bg-white p-4">
      <view v-if="isShowStep" class="mb-6 pt-2">
        <view class="mb-2 font-bold">
          制作步骤：
        </view>
        <view
          v-for="(item, index) in DESCRIPTION_STR[descriptionServiceType].makeStep" :key="index"
          class="flex text-sm"
        >
          <view class="min-w-5 shrink-0 text-primary font-bold">
            {{ index + 1 }}.
          </view>
          <view>{{ item }}</view>
        </view>
        <!--        <view class="mt-6">
          <view class="font-bold text-sm mb-2">收费标准：</view>
          <view class="text-xs">{{ DESCRIPTION_STR.chargingStandard }}</view>
        </view> -->
      </view>
      <view class="o-color-aid flex items-center gap-3 text-sm" @click="isShowStep = !isShowStep">
        <up-icon name="question-circle" size="18" />
        <view>{{ isShowStep ? '隐藏' : '显示' }}条码制作说明</view>
        <up-icon v-if="isShowStep" name="arrow-up" size="16" />
        <up-icon v-else name="arrow-down" size="16" />
      </view>
    </view>
    <!--    <view
          class="f-red-card py-1 px-2 text-red-500 text-sm my-2 rd-1 flex justify-between items-center"
          @click="toPath(serverData)"
        >
          <text>直接印刷标签贴纸，点这里</text>
          <up-icon name="arrow-right" size="14"></up-icon>
        </view> -->
    <cs-long-button />
    <view class="mt-3 rd-2 bg-white p-4">
      <view class="mb-3 pt-4 font-bold">
        要生成条码的类型：
      </view>
      <up-subsection
        :active-color="Color.primary" :bg-color="Color.bgBlue" :current="advancedServiceCurrent"
        :list="advancedServiceList" @change="handleAdvancedService"
      />
      <view v-show="advancedService === AdvancedService.withReport" class="ml-6 mt-2 text-sm">
        <view class="flex gap-2">
          <view class="o-dot" />
          <view>含电子条码生成</view>
        </view>
        <view class="flex gap-2">
          <view class="o-dot" />
          <view>含信息通报</view>
        </view>
        <view class="flex gap-2">
          <view class="o-dot" />
          <view>含微信扫码展示商品信息功能</view>
        </view>
        <view class="flex gap-2">
          <view class="o-dot" />
          <view>含条码符合性确认与条码查重</view>
        </view>
      </view>
      <view v-show="advancedService !== AdvancedService.withReport" class="ml-6 mt-2 text-sm">
        <view class="flex gap-2">
          <view class="o-dot" />
          <view>仅电子条码生成</view>
        </view>
      </view>
      <view class="pt-4 font-bold">
        要生成条码的编码：
      </view>
      <up-form label-width="100">
        <up-form-item
          v-if="descriptionServiceType === BarType.ITF14" label="包装指示符"
          @click="isShowPackingCodePick = true"
        >
          <up-input v-model="packingCode" border="bottom" :disabled="true" disabled-color="#fff" />
          <template #right>
            <up-icon name="arrow-right" size="14" />
          </template>
        </up-form-item>
        <up-picker
          :columns="packingCodeArr" :show="isShowPackingCodePick" :confirm-color="Color.primary"
          close-on-click-overlay title="1~8:定量包装，9:变量" @cancel="isShowPackingCodePick = false"
          @close="isShowPackingCodePick = false" @confirm="packingCodeConfirm"
        />
        <up-form-item label="厂商识别代码" @click="handleVendorCodeClick">
          <up-input
            v-model="vendorCode" :color="errorTypeColor.vendorCode" :maxlength="9" border="bottom"
            placeholder="7~9位" type="number" @blur="handleInputManufacturerIdCode"
          />
        </up-form-item>
        <view v-if="searchCompanyName" class="mb-3 mt-1 text-right text-xs color-gray">
          {{ searchCompanyName }}
        </view>
        <view
          class="f-btn-blue mb-2 flex items-center justify-between rd-1 px-2 py-1 text-sm text-primary"
          @click="toGetVendorCode"
        >
          <text>不知厂商识别代码？可点击查询</text>
          <up-icon name="arrow-right" size="14" />
        </view>

        <up-form-item label="项目代码" @click="handleVendorCodeClick">
          <view class="flex items-center">
            <up-input
              v-model="projectCode.from" :color="errorTypeColor.from" :maxlength="5"
              :placeholder="placeholderFrom" border="bottom" clearable type="number"
              @blur="handleProjectCodeBlur('from')" @focus="errorTypeColor.from = Color.inputColor"
              @change="handleInput"
            />
            <view>~</view>
            <up-input
              v-model="projectCode.to" :color="errorTypeColor.to" :maxlength="5" :placeholder="placeholderTo"
              border="bottom" clearable type="number" @blur="handleProjectCodeBlur('to')"
              @focus="errorTypeColor.to = Color.inputColor" @change="handleInput"
            />
          </view>
        </up-form-item>
        <view class="mt-1 flex items-center justify-between gap-1">
          <view class="text-xs color-gray">
            <view>条码数量</view>
            <view>快捷选择</view>
          </view>
          <view class="f-btn-blue f-btn" @click="handleEasyInput(1)">
            1
          </view>
          <view class="f-btn-blue f-btn" @click="handleEasyInput(10)">
            10
          </view>
          <view class="f-btn-blue f-btn relative" @click="handleEasyInput(50)">
            50
            <up-tag
              class="absolute right--1 top--2.5 whitespace-nowrap" style="transform-origin: right top; scale: 0.7"
              text="推荐" type="error" size="mini"
            />
          </view>
          <view class="f-btn-blue f-btn" @click="handleEasyInput(100)">
            100
          </view>
          <view class="f-btn-blue f-btn relative" @click="handleEasyInput(200)">
            200
            <up-tag
              class="absolute right--1 top--2.5 whitespace-nowrap" style="transform-origin: right top; scale: 0.7"
              text="最优惠" type="error" size="mini"
            />
          </view>
        </view>
        <up-form-item label="放大系数" @click="scrollToElement">
          <up-input
            v-model="coefficient" border="bottom" disabled disabled-color="#fff"
            @change="handleInputManufacturerIdCode"
          >
            <template #suffix>
              <view class="flex items-center gap-1">
                <view class="shrink-0 text-xs color-gray">
                  (如无特殊默认即可)
                </view>
                <view class="color-gray">
                  <up-icon v-if="isShowSelectSize" name="arrow-up" size="16" />
                  <up-icon v-else name="arrow-down" size="16" />
                </view>
              </view>
            </template>
          </up-input>
        </up-form-item>
      </up-form>
      <view v-if="isShowSelectSize">
        <view class="mt-4 flex items-center gap-5">
          <view class="text-xs">
            <text class="text-red-500">
              *
            </text>
            {{ BlowUpStr }}
          </view>
          <view class="relative shrink-0">
            <up-image
              :height="DESCRIPTION_STR[descriptionServiceType].sizeImg.height"
              :src="DESCRIPTION_STR[descriptionServiceType].sizeImg.data"
              :width="DESCRIPTION_STR[descriptionServiceType].sizeImg.width" class="shrink-0"
            />
            <view class="f-w absolute text-xs">
              {{ size.width }}
            </view>
            <view class="f-h absolute text-xs">
              {{ size.height }}
            </view>
          </view>
        </view>
        <view id="targetElement" class="mt-6">
          <view class="mb-3 flex justify-between font-bold">
            <view class="f-table-o" />
            <view class="f-table-n text-center">
              放大系数
            </view>
            <view class="f-table-w text-center">
              宽（mm）
            </view>
            <view class="f-table-h text-center">
              高（mm）
            </view>
          </view>
          <up-radio-group v-model="coefficient" class="o-vf-up-radio-group" placement="column">
            <up-radio
              v-for="(item, index) in DESCRIPTION_STR[descriptionServiceType].blowUp" :key="index"
              :name="item.coefficient" @change="handleSelect(item)"
            >
              <template #label>
                <view class="flex">
                  <view class="f-table-n text-center">
                    {{ item.coefficient }}
                  </view>
                  <view class="f-table-w text-center">
                    {{ item.w }}
                  </view>
                  <view class="f-table-h text-center">
                    {{ item.h }}
                  </view>
                </view>
              </template>
            </up-radio>
          </up-radio-group>
        </view>
      </view>
    </view>
    <view class="p-10" />
    <view class="fixed bottom-0 left-0 z-10 box-border w-full p-4">
      <view
        :class="loading ? 'o-bg-primary-disable' : 'o-bg-primary o-shadow-blue'"
        class="flex flex-grow-1 items-center justify-center rd-2 p-3 text-white font-bold" @click="handleSubmit"
      >
        生成预览{{ priceStr }}
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.f-w {
  bottom: -0.8rem;
  left: 100rpx;
}

.f-h {
  top: 70rpx;
  left: -2.5rem;
  transform: rotate(-90deg);
}

.f-table-o {
  width: 50rpx;
}

.f-table-n {
  width: 190rpx;
}

.f-table-w {
  width: 180rpx;
}

.f-table-h {
  width: 180rpx;
}

.f-red-card {
  background-color: rgba(245, 63, 63, 0.05);
  border: 1px dashed var(--wot-color-danger);
}

.f-btn-blue {
  background-color: rgba(22, 93, 255, 0.05);
  border: 1px solid rgba(22, 93, 255, 0.3);

  &.f-btn {
    @apply py-1 text-primary text-sm my-2 rd-1 text-center flex-1;

    &:active {
      background-color: rgba(22, 93, 255, 0.5);
    }
  }
}
</style>
