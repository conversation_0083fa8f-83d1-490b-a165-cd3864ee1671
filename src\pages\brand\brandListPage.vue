<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "品牌列表"
  }
}
</route>

<script lang="ts" setup>
import type { BrandListParams, BrandListRes } from '@/service/infoReportApi'
import debounce from 'debounce'
import { storeToRefs } from 'pinia'
import { Color } from '@/enums/colorEnum'
import { brandListApi } from '@/service/infoReportApi'
import { infoReportEditStore } from '@/store/infoReportEditStore'
import { useUserStore } from '@/store/user'

const userStore = useUserStore()
const { userId } = storeToRefs(userStore)
const useInfoReportEditStore = infoReportEditStore()
const { brandName } = storeToRefs(useInfoReportEditStore)
const keyword = ref('')
const loading = ref(false)
const dataList = ref<BrandListRes['data']>([])
const hasMore = ref(true)

const handleInput = debounce(() => {
  dataList.value = []
  getBrandList()
}, 800)

function handleSelect(name: string) {
  brandName.value = name
  uni.navigateBack()
}

function getBrandList() {
  loading.value = true
  const params: BrandListParams = {
    userId: userId.value,
  }
  if (keyword.value !== '') {
    params.brandName = keyword.value
  }

  brandListApi(params).then((res) => {
    dataList.value = res.data
  }).finally(() => {
    loading.value = false
  })
}

function addBrand() {
  uni.navigateTo({
    url: '/pages/brand/brandEditPage?action=create',
  })
}

onShow(() => {
  getBrandList()
})
</script>

<template>
  <view>
    <view
      class="f-search-bar fixed left-0 top-0 z-1 box-border w-full flex items-center bg-white px-3 py-2 shadow-blue space-x-2"
    >
      <view class="o-bg-no flex flex-1 items-center gap-2 py-1 pl-4 pr-3" style="border-radius: 2rem">
        <up-icon :color="Color.gray" name="search" size="20" />
        <up-input
          v-model="keyword" :maxlength="30" border="none" class="o-bg-transparent grow" clearable
          placeholder="请输入品牌名搜索" @change="handleInput"
        />
        <up-icon color="#ccc" name="close-circle-fill" size="20" @click="keyword = ''" />
      </view>
      <view class="o-bg-primary h-8 center rd-2 px-4 text-white" @click="addBrand">
        新建品牌
      </view>
    </view>
    <up-list class="box-border px-6 pt-14" :enable-back-to-top="true">
      <up-list-item v-for="(item) in dataList" :key="item.brandId">
        <view class="f-item flex items-baseline justify-between py-2" @click="handleSelect(item.brandName)">
          <view>
            {{ item.brandName }}
          </view>
          <up-icon name="arrow-right" color="#9ca3af" size="12" />
        </view>
      </up-list-item>

      <!-- 加载状态提示 -->
      <view v-if="loading" class="flex justify-center py-4">
        <up-loading-icon mode="semicircle" />
        <text class="ml-2 text-gray-500">加载中...</text>
      </view>

      <!-- 没有更多数据提示 -->
      <view v-else-if="!hasMore && dataList?.length" class="flex justify-center py-8">
        <text class="text-gray-400">没有更多数据了</text>
      </view>
    </up-list>
  </view>
</template>

<style lang="scss" scoped>
.f-item {
  border-bottom: 1px solid #eee;
}
</style>
