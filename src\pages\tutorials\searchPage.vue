<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "搜索"
  }
}
</route>

<script lang="ts" setup>
import type { ArticlePageRes } from '@/service/tutorialsApi'
import ArticleCard from '@/components/tutorials/articleCard.vue'
import { OrderDirection } from '@/enums/httpEnum'
import { articlePageApi } from '@/service/tutorialsApi'

const articleTitle = ref('')
const page = ref<number>(1)
const showEmptyIcon = ref(false)
const list = ref<any[]>([])

const { loading, error, data, run } = useRequest<ArticlePageRes>(() =>
  articlePageApi({
    articleTitle: articleTitle.value,
    groupBy: '',
    needTotalCount: true,
    orderBy: '',
    orderDirection: OrderDirection.asc,
    pageIndex: page.value,
    pageSize: 20,
  }),
)

function init() {
  return new Promise((resolve, reject) => {
    list.value = []
    page.value = 1
    showEmptyIcon.value = false
    run()
      .then(() => {
        list.value = [...data.value]
        resolve(true)
      })
      .catch((err) => {
        reject(err)
      })
  })
}

// 滚到页面底部加载更多
onReachBottom(() => {
  if (!showEmptyIcon.value) {
    page.value++
    run().then(() => {
      list.value = [...list.value, ...data.value]
      if (data.value.length === 0) {
        showEmptyIcon.value = true
      }
    })
  }
})

const scrollTop = ref<number>(0)
onPageScroll((e) => {
  scrollTop.value = e.scrollTop
})

function handleSearch() {
  init()
}
</script>

<template>
  <view class="sticky left-0 top-0 z-1 w-full">
    <view class="bg-white px-4 py-2">
      <view class="o-bg-no flex items-center gap-2 py-1 pl-4 pr-3" style="border-radius: 2rem">
        <up-input
          v-model="articleTitle"
          border="none"
          class="o-bg-transparent grow"
          placeholder="搜索标题"
          clearable
          :maxlength="50"
        />
        <up-icon name="search" size="20" @click="handleSearch" />
      </view>
    </view>
  </view>
  <view class="px-3 pb-6 pt-3">
    <article-card v-for="item in list" :key="item.articleId" :data="item" />
    <view v-if="showEmptyIcon" class="o-color-aid w-full text-center">
      - 已经到底了 -
    </view>
  </view>
  <up-back-top :scroll-top="scrollTop" />
</template>

<style lang="scss" scoped></style>
