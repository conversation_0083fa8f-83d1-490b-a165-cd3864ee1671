import type {
  GetUserServerRes,
  LoginParams,

  LoginRes,
} from '@/service/systemApi'
import { defineStore } from 'pinia'
import { ref } from 'vue'
import { FromPlatform } from '@/enums'
import {
  getUserServerInfoApi,
  wxparentsUserDouYinLogin,
  wxParentsUserLoginPath,
} from '@/service/systemApi'

export const useUserStore = defineStore(
  'user',
  () => {
    // 黑名单
    const inBlacklist = ref(false)
    // 厂商唯一识别，用于记录黑名单
    const dataCode = ref('')
    const expiration = 30 * 60 * 1000 // 过期时间为30分钟
    const userId = ref<number>(0)
    const userCode = ref('')
    const realName = ref('')
    const authorization = ref('')
    const downloadEmail = ref('')
    const defaultInvoiceTempId = ref(0)
    const timeout = ref(0)
    // 本地缓存手机号
    const localPhone = ref('')
    // 信息通报权限
    const reportAuth = ref(false)
    // 注册权限
    const registerServiceAuth = ref(false)
    // 是否有未完成的业务
    const existUncompletedRegister = ref(false)
    // 续展权限
    const renewalServiceAuth = ref(false)
    // 是否有未完成的业务
    const existUncompletedRenewal = ref(false)
    // 变更权限
    const modifyServiceAuth = ref(false)
    // 是否有未完成的业务
    const existUncompletedChange = ref(false)

    const login = (companyCode?: string) =>
      new Promise((resolve, reject) => {
        // 过期及快过期才重新登录
        if (Date.now() > timeout.value) {
          // TODO 抖音登录第一次回登录失败，返回参数错误？
          uni.login({
            success(res) {
              console.log(res)
              const data: LoginParams = {
                code: res.code,
              }
              let url = ''
              // #ifdef MP-WEIXIN
              // 微信
              data.fromTo = import.meta.env.VITE_FROM_PLATFORM
              url = wxParentsUserLoginPath
              // #endif
              // #ifdef MP-TOUTIAO
              // 抖音
              data.fromTo = FromPlatform.douYin
              url = wxparentsUserDouYinLogin
              // #endif
              if (companyCode) {
                data.dataCode = companyCode
              }
              uni.request({
                url,
                method: 'POST',
                data,
                success: (res: any) => {
                  const dataRes = res.data as LoginRes
                  if (dataRes.success) {
                    const d = dataRes.data
                    // inBlacklist.value = true
                    inBlacklist.value = d.inBlacklist
                    userId.value = Number(d.userId)
                    userCode.value = d.userCode
                    realName.value = d.realName
                    authorization.value = d.authorization
                    timeout.value = Date.now() + expiration
                    resolve(res)
                  }
                  else {
                    reject(res)
                  }
                },
                fail: (err) => {
                  reject(err)
                },
              })
            },
            fail(err) {
              reject(err)
            },
          })
        }
        else {
          resolve(true)
        }
      })

    const getUserServerInfo = () =>
      new Promise<GetUserServerRes>((resolve, reject) => {
        getUserServerInfoApi({
          // certificationId: 0,
          userId: userId.value,
        })
          .then((res) => {
            // console.log('existUncompletedRegister', res.data.existUncompletedRegister)
            // console.log('existUncompletedRenewal', res.data.existUncompletedRenewal)
            // console.log('existUncompletedChange', res.data.existUncompletedChange)
            reportAuth.value = res.data.reportAuth
            registerServiceAuth.value = res.data.registerAuth
            renewalServiceAuth.value = res.data.renewalAuth
            modifyServiceAuth.value = res.data.changeAuth
            dataCode.value = res.data.dataCode
            existUncompletedRegister.value = res.data.existUncompletedRegister
            existUncompletedRenewal.value = res.data.existUncompletedRenewal
            existUncompletedChange.value = res.data.existUncompletedChange
            resolve(res)
          })
          .catch((err) => {
            reject(err)
          })
      })

    return {
      inBlacklist,
      dataCode,
      userId,
      userCode,
      realName,
      authorization,
      downloadEmail,
      defaultInvoiceTempId,
      localPhone,
      reportAuth,
      registerServiceAuth,
      renewalServiceAuth,
      modifyServiceAuth,
      existUncompletedRegister,
      existUncompletedRenewal,
      existUncompletedChange,
      login,
      getUserServerInfo,
    }
  },
  {
    persist: true,
  },
)
