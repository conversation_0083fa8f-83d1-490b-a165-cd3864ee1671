<script setup lang="ts">
defineProps<{
  carousesList: string[]
}>()

const current = ref(0)
const { windowWidth } = uni.getSystemInfoSync()
function handleSelectImg(index: number) {
  current.value = index
}
</script>

<template>
  <view class="bg-white">
    <up-swiper
      v-model:current="current"
      :height="windowWidth"
      :list="carousesList"
      :autoplay="false"
    />
    <view class="flex gap-2 px-4 py-3">
      <image
        v-for="(item, index) in carousesList"
        :key="index"
        class="f-cs-img"
        :style="{ opacity: current === index ? 1 : 0.4 }"
        :src="item"
        mode="aspectFill"
        @click="handleSelectImg(index)"
      />
    </view>
  </view>
</template>

<style scoped lang="scss">
.f-cs-img {
  $w: 100rpx;
  width: $w;
  height: $w;
}
</style>
