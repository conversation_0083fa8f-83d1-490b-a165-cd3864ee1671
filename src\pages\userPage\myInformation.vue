<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "我的信息"
  }
}
</route>

<script lang="ts" setup></script>

<template>
  <view class="p-4">
    <up-cell-group custom-class="bg-white py-2 rd-2" :border="false">
      <up-cell title="开票信息" is-link url="/pages/invoiceTemple/index" />
      <!--  #ifndef MP-TOUTIAO -->
      <up-cell title="收货地址" is-link url="/pages/addressPage/index" :border="false" />
      <!--  #endif -->
    </up-cell-group>
  </view>
</template>

<style lang="scss" scoped></style>
