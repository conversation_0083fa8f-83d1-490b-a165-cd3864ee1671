import { validatePhoneNumber } from '@/utils/tool'

export function useAddressInfoHook() {
  const model = reactive<{
    addressDetail: string
    district: string
    isDefault: boolean
    phone: string
    realName: string
  }>({
    addressDetail: '',
    district: '',
    isDefault: false,
    phone: '',
    realName: '',
  })

  const form = ref()
  const rules = {
    // 字段名称
    district: [
      {
        required: true,
        message: '请输入省市区',
        trigger: ['blur'],
      },
    ],
    addressDetail: [
      {
        required: true,
        message: '请输入详细地址与门牌号',
        trigger: ['blur'],
      },
    ],
    realName: [
      {
        required: true,
        message: '请输入姓名',
        trigger: ['blur'],
      },
    ],
    phone: [
      {
        required: true,
        message: '请输入联系手机',
        trigger: ['blur'],
      },
      {
        validator: validatePhoneNumber,
        message: '请输入正确的手机号',
        // 触发器可以同时用blur和change
        trigger: ['blur'],
      },
    ],
  }

  return {
    model,
    form,
    rules,
    validatePhoneNumber,
  }
}
