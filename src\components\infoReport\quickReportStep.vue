<script lang="ts" setup>
</script>

<template>
  <view class="mb-6 pt-2">
    <view class="mb-2 font-bold">
      逐个产品通报步骤：
    </view>
    <view class="flex text-sm">
      <view class="min-w-5 shrink-0 text-primary font-bold">
        1.
      </view>
      <view>本小程序返回到首页，从屏幕下方的固定栏目【条码管理】或【产品通报】进入。</view>
    </view>
    <view class="flex text-sm">
      <view class="min-w-5 shrink-0 text-primary font-bold">
        2.
      </view>
      <view>按要求填写产品信息。</view>
    </view>
    <view class="flex text-sm">
      <view class="min-w-5 shrink-0 text-primary font-bold">
        3.
      </view>
      <view>保存提交后，等待审批结果即可。</view>
    </view>
  </view>
</template>

<style scoped lang="scss"></style>
