import { defineStore } from 'pinia'
import { gpcNationalListApi } from '@/service/infoReportApi'

export interface GpcNationalChildrenDataWithLevel {
  gpcCode: string
  name: string
  level: number
  children: GpcNationalChildrenDataWithLevel[]
}
export const nationalGpcStore = defineStore('nationalGpcStore', () => {
  // TODO 测试是否不同页面未共享数据
  const nationalGpcData = ref<GpcNationalChildrenDataWithLevel[]>([])
  const levelNationalGpcData = ref<GpcNationalChildrenDataWithLevel[]>([])
  function getLevelNationalGpcData() {
    return levelNationalGpcData.value
  }

  return { nationalGpcData, levelNationalGpcData, getLevelNationalGpcData }
})
