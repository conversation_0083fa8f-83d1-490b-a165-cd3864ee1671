import { http } from '@/http/http'

// 响应接口
export interface GetStsTokenRes {
  data: {
    accessKeyId: string
    accessKeySecret: string
    expiration: string
    securityToken: string
  }
  errCode: string
  errMessage: string
  success: boolean
}

/**
 * 获取阿里云OSS的STS临时访问凭证
 * @returns STS临时凭证响应，1小时内有效
 */
export const getStsTokenApi = () => http.post<GetStsTokenRes>('/sts/getStsTokenForOss')

// 通报图片上传OSS的路径
export const InfoReportGoodsImageUploadPath = 'infoReport/goodsImage'
