import { defineStore } from 'pinia'
import { ref } from 'vue'

// GPC数据类型定义
export interface GpcDataType {
  code: string
  label: string
  children: GpcDataType[]
}

// 原始数据类型定义
export interface RawGpcDataType {
  ID: number
  PID: number
  Code: string
  Description_en: string
  Description: string
  hierarchy: number
  childNode: null
}

export const infoReportEditStore = defineStore('infoReportEditStore', () => {
  const activeGpcItem = ref({
    code: '',
    gpcTypeName: '',
  })
  const brandName = ref<string>('')

  function getActiveGpcItem() {
    return activeGpcItem.value
  }

  function getBrandName() {
    return brandName.value
  }

  function clearReportEditData() {
    activeGpcItem.value = {
      code: '',
      gpcTypeName: '',
    }
    brandName.value = ''
  }

  return {
    activeGpcItem,
    brandName,
    getActiveGpcItem,
    getBrandName,
    clearReportEditData,
  }
})
