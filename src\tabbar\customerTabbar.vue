<script setup lang="ts">
// 'i-carbon-code',
import { tabbarList as _tabBarList, customTabbarEnable, nativeTabbarNeedHide, tabbarCacheEnable } from './config'
import { tabbarStore } from './store'

// #ifdef MP-WEIXIN
// 将自定义节点设置成虚拟的（去掉自定义组件包裹层），更加接近Vue组件的表现，能更好的使用flex属性
defineOptions({
  virtualHost: true,
})
// #endif

/** tabbarList 里面的 path 从 pages.config.ts 得到 */
const tabbarList = _tabBarList.map(item => ({ ...item, path: `/${item.pagePath}` }))
function handleClick(index: number) {
  // 点击原来的不做操作
  if (index === tabbarStore.curIdx) {
    return
  }
  const url = tabbarList[index].path
  tabbarStore.setCurIdx(index)
  if (tabbarCacheEnable) {
    uni.switchTab({ url })
  }
  else {
    uni.navigateTo({ url })
  }
}

// 根据当前页面路径设置正确的tabbarIndex
function updateTabbarIndex() {
  const pages = getCurrentPages()
  if (pages.length > 0) {
    const currentPage = pages[pages.length - 1]
    const currentPath = `/${currentPage.route}`

    // 查找当前路径在tabbarList中的索引
    const index = tabbarList.findIndex(item => item.path === currentPath)
    if (index !== -1 && index !== tabbarStore.curIdx) {
      tabbarStore.setCurIdx(index)
    }
  }
}

onLoad(() => {
  // 解决原生 tabBar 未隐藏导致有2个 tabBar 的问题
  nativeTabbarNeedHide
  && uni.hideTabBar({
    fail(err) {
      console.log('hideTabBar fail: ', err)
    },
    success(res) {
      console.log('hideTabBar success: ', res)
    },
  })

  // 初始化时更新tabbar索引
  updateTabbarIndex()
})

// 每次页面显示时更新tabbar索引
onShow(() => {
  updateTabbarIndex()
})
</script>

<template>
  <view
    v-if="customTabbarEnable" class="fixed bottom-0 left-0 right-0 z-100 box-border w-full center px-3 pb-safe"
  >
    <view class="f-tabbar o-tabbar-shadow mb-2 flex flex-1 items-center justify-around overflow-hidden px-4">
      <view v-for="(item, index) in tabbarList" :key="index" class="center flex-1" @click="handleClick(index)">
        <image class="f-image" :src="tabbarStore.curIdx === index ? item.selectedIconPath : item.iconPath" mode="aspectFit" />
        <!-- 预加载未选中和选中状态的图标 -->
        <image class="f-hidden" :src="item.iconPath" />
        <image class="f-hidden" :src="item.selectedIconPath" />
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.f-tabbar {
  height: var(--fv-tabbar-height);
  border-radius: var(--fv-tabbar-height);
  background: rgba(255, 255, 255, 0.65);
  backdrop-filter: blur(25rpx);
}
.f-image {
  $w: 130rpx;
  width: $w;
  height: $w;
}

.f-hidden {
  width: 0;
  height: 0;
  opacity: 0;
  position: absolute;
  visibility: hidden;
}
</style>
